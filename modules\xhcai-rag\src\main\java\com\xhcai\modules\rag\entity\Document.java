package com.xhcai.modules.rag.entity;

import java.time.LocalDateTime;
import java.util.Map;

import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;
import com.xhcai.common.datasource.handler.PostgreSQLJsonTypeHandler;
import com.xhcai.modules.rag.entity.inner.CleaningConfig;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import lombok.Data;
import org.hibernate.annotations.JdbcTypeCode;
import org.hibernate.type.SqlTypes;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Table;

/**
 * 文档实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Schema(description = "文档实体")
@Entity
@Data
@Table(name = "documents")
@TableName(value = "documents", autoResultMap = true)
public class Document extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 知识库ID
     */
    @Schema(description = "知识库ID", example = "dataset123")
    @Column(name = "dataset_id", nullable = false, length = 32)
    @TableField("dataset_id")
    private String datasetId;

    /**
     * 文件来源类型
     */
    @Schema(description = "数据来源类型", example = "从用户上传、数据源定时抽取等")
    @Column(name = "data_source_type", nullable = false, length = 255)
    @TableField("data_source_type")
    private String dataSourceType;

    /**
     * 数据集处理规则ID
     */
    @Column(name = "dataset_process_rule_id", length = 32)
    @TableField("dataset_process_rule_id")
    private String datasetProcessRuleId;

    /**
     * 批次上传时间序号
     */
    @Column(name = "batch", nullable = false, length = 255)
    @TableField("batch")
    private String batch;

    /**
     * 文件名称
     */
    @Column(name = "name", nullable = false, length = 255)
    @TableField("name")
    private String name;

    /**
     * 文档元数据
     */
    @JdbcTypeCode(SqlTypes.JSON)
    @Column(name = "doc_metadata", columnDefinition = "json")
    @TableField(value = "doc_metadata", typeHandler = PostgreSQLJsonTypeHandler.class, jdbcType = org.apache.ibatis.type.JdbcType.OTHER)
    private Map<String, Object> docMetadata;

    /**
     * 分段配置
     */
    @Schema(description = "分段配置")
    @Column(name = "segment_config", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    @TableField(value = "segment_config", typeHandler = com.xhcai.common.datasource.handler.GenericJsonbTypeHandler.class, jdbcType = org.apache.ibatis.type.JdbcType.OTHER)
    private SegmentConfig segmentConfig;

    /**
     * 清洗配置
     */
    @Schema(description = "清洗配置")
    @Column(name = "cleaning_config", columnDefinition = "jsonb")
    @JdbcTypeCode(SqlTypes.JSON)
    @TableField(value = "cleaning_config", typeHandler = com.xhcai.common.datasource.handler.GenericJsonbTypeHandler.class, jdbcType = org.apache.ibatis.type.JdbcType.OTHER)
    private CleaningConfig cleaningConfig;

    /**
     * 新建开始时间
     */
    @Column(name = "processing_started_at")
    @TableField("processing_started_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processingStartedAt;

    /**
     * 字符数
     */
    @Column(name = "word_count")
    @TableField("word_count")
    private Integer wordCount;

    /**
     * 分段数量
     */
    @Schema(description = "分段数量", example = "15")
    @Column(name = "segment_count")
    @TableField("segment_count")
    private Integer segmentCount;

    /**
     * 文件内容hash值，用于验证文件是否重复
     */
    @Schema(description = "文件内容hash值", example = "a1b2c3d4e5f6...")
    @Column(name = "file_hash", length = 64)
    @TableField("file_hash")
    private String fileHash;

    /**
     * 解析读取时间
     */
    @Column(name = "parsing_completed_at")
    @TableField("parsing_completed_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime parsingCompletedAt;


    /**
     * 文档处理状态  对应枚举类 DocumentStatus
     */
    @Column(name = "document_status", length = 32)
    @TableField("document_status")
    private String documentStatus;

    /**
     * 清理完成时间
     */
    @Column(name = "cleaning_completed_at")
    @TableField("cleaning_completed_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cleaningCompletedAt;

    /**
     * 分段完成时间
     */
    @Column(name = "splitting_completed_at")
    @TableField("splitting_completed_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime splittingCompletedAt;

    /**
     * token数
     */
    @Column(name = "tokens")
    @TableField("tokens")
    private Integer tokens;

    /**
     * 索引化耗时
     */
    @Column(name = "indexing_latency")
    @TableField("indexing_latency")
    private Double indexingLatency;

    /**
     * 完成时间
     */
    @Column(name = "completed_at")
    @TableField("completed_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedAt;

    /**
     * 是否中止
     */
    @Column(name = "is_paused")
    @TableField("is_paused")
    private Boolean isPaused = false;

    /**
     * 中止人ID
     */
    @Column(name = "paused_by", length = 32)
    @TableField("paused_by")
    private String pausedBy;

    /**
     * 中止时间
     */
    @Column(name = "paused_at")
    @TableField("paused_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pausedAt;

    /**
     * 上传人
     */
    @Column(name = "upload_by", length = 32)
    @TableField("upload_by")
    private String uploadBy;

    /**
     * 上传时间
     */
    @Column(name = "upload_time")
    @TableField("upload_time")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTime;

    /**
     * 异常原因
     */
    @Column(name = "error", columnDefinition = "TEXT")
    @TableField("error")
    private String error;

    /**
     * 停止时间
     */
    @Column(name = "stopped_at")
    @TableField("stopped_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime stoppedAt;

    /**
     * 是否可用
     */
    @Column(name = "enabled", nullable = false)
    @TableField("enabled")
    private Boolean enabled = true;

    /**
     * 不可用操作时间
     */
    @Column(name = "disabled_at")
    @TableField("disabled_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime disabledAt;

    /**
     * 不可用操作人ID
     */
    @Column(name = "disabled_by", length = 32)
    @TableField("disabled_by")
    private String disabledBy;

    /**
     * 是否归档
     */
    @Column(name = "archived", nullable = false)
    @TableField("archived")
    private Boolean archived = false;

    /**
     * 归档原因
     */
    @Column(name = "archived_reason", length = 255)
    @TableField("archived_reason")
    private String archivedReason;

    /**
     * 归档人
     */
    @Column(name = "archived_by", length = 32)
    @TableField("archived_by")
    private String archivedBy;

    /**
     * 归档时间
     */
    @Column(name = "archived_at")
    @TableField("archived_at")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime archivedAt;

    /**
     * 文档类型
     */
    @Column(name = "doc_type", length = 40)
    @TableField("doc_type")
    private String docType;

    /**
     * 文档大小
     */
    @Column(name = "file_size")
    @TableField("file_size")
    private Long fileSize;

    /**
     * 文档形式
     */
    @Column(name = "doc_form", nullable = false, length = 255)
    @TableField("doc_form")
    private String docForm = "text_model";

    /**
     * 文档语言
     */
    @Column(name = "doc_language", length = 255)
    @TableField("doc_language")
    private String docLanguage;

    /**
     * 文件分类ID
     */
    @Schema(description = "文件分类ID", example = "category123")
    @Column(name = "category_id", length = 32)
    @TableField("category_id")
    private String categoryId;

    /**
     * 获取预览URL
     */
    public String getPreviewUrl() {
        if (docMetadata == null) {
            return null;
        }
        return (String) docMetadata.get("preview_url");
    }
}
