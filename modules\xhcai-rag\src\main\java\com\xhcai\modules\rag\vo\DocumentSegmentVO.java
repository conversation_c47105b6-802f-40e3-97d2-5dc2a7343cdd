package com.xhcai.modules.rag.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.xhcai.common.datasource.handler.GenericJsonbTypeHandler;
import com.xhcai.modules.rag.entity.Document;
import com.xhcai.modules.rag.entity.DocumentSegment;
import com.xhcai.modules.rag.entity.KnowledgeVectorizationConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.apache.ibatis.type.JdbcType;

@EqualsAndHashCode(callSuper = true)
@Data
@Schema(description = "文档分段VO")
public class DocumentSegmentVO extends DocumentSegment {

    private static final long serialVersionUID = 1L;

    @Schema(description = "文档")
    @TableField(value = "document", typeHandler = GenericJsonbTypeHandler.class, jdbcType = JdbcType.OTHER)
    private Document document;

    @Schema(description = "向量库信息", example = "docSegment123")
    private VectorDatabaseVO vectorDatabase;

    @Schema(description = "索引节点信息", example = "node123")
    private KnowledgeVectorizationConfig knowledgeVectorizationConfig;
}
