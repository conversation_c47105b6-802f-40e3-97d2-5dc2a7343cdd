# API密钥认证架构设计

## 一、整体架构设计

### 1. 密钥Key生成规则

#### 1.1 授权密钥Key结构
```json
{
  "sub": "userId",           // 用户ID
  "username": "username",    // 用户名
  "tenantId": "tenantId",   // 租户ID
  "deptId": "deptId",       // 部门ID
  "targetType": "agent",     // 访问对象类型：agent/knowledge/graph/model
  "targetId": "objectId",    // 访问对象ID（智能体ID、知识库ID等）
  "permissions": ["perm1", "perm2"], // 权限列表
  "iat": 1640995200,        // 签发时间
  "exp": 1672531200,        // 过期时间（长期有效，如1年）
  "type": "api_key"         // 标识为API密钥类型
}
```

#### 1.2 第三方业务密钥Key结构
第三方业务系统需要将授权密钥Key和当前时间组合生成新的密钥：
```
新密钥Key = Base64(授权密钥Key + "." + 当前时间戳)
```

### 2. 时效性验证机制

#### 2.1 授权密钥验证
- 授权密钥Key本身具有长期有效期（如1年）
- 验证JWT签名和基本有效性

#### 2.2 时效性验证
- 从新密钥Key中解析出第三方业务加上的时间戳
- 验证时间戳与当前时间的差值不超过30分钟
- 公式：`当前时间 - 第三方时间戳 <= 30分钟`

### 3. 权限一致性保证

#### 3.1 权限继承
- API密钥的权限完全继承申请用户的权限
- 通过`targetType`和`targetId`限制访问范围
- 支持细粒度权限控制

#### 3.2 权限验证流程
1. 验证授权密钥Key的有效性
2. 验证时效性（30分钟限制）
3. 验证用户权限是否包含所需权限
4. 验证是否有权访问指定的目标对象

## 二、数据库表设计

### 1. API密钥管理表
```sql
CREATE TABLE sys_api_key (
    id VARCHAR(36) PRIMARY KEY,
    key_name VARCHAR(100) NOT NULL COMMENT '密钥名称',
    key_value TEXT NOT NULL COMMENT '授权密钥Key（JWT格式）',
    key_hash VARCHAR(64) NOT NULL COMMENT '密钥哈希值（用于快速查找）',

    -- 申请信息
    applicant_unit VARCHAR(100) NOT NULL COMMENT '申请单位',
    applicant_name VARCHAR(50) NOT NULL COMMENT '申请人姓名',
    applicant_phone VARCHAR(20) NOT NULL COMMENT '申请人手机号',

    -- 责任信息
    responsible_unit VARCHAR(100) NOT NULL COMMENT '责任单位',
    responsible_name VARCHAR(50) NOT NULL COMMENT '责任人姓名',
    responsible_phone VARCHAR(20) NOT NULL COMMENT '责任人手机号',

    -- 访问配置
    target_type VARCHAR(20) NOT NULL COMMENT '访问对象类型：agent/knowledge/graph/model',
    target_id VARCHAR(36) NOT NULL COMMENT '访问对象ID',
    target_name VARCHAR(100) NOT NULL COMMENT '访问对象名称',

    access_frequency VARCHAR(20) NOT NULL COMMENT '访问频率：low/medium/high',
    access_count_limit INT DEFAULT 0 COMMENT '访问次数限制（0表示无限制）',
    access_count_used INT DEFAULT 0 COMMENT '已使用访问次数',

    -- 状态管理
    status VARCHAR(20) DEFAULT 'pending' COMMENT '状态：pending/approved/rejected/disabled',

    -- 审批信息
    approve_by VARCHAR(36) COMMENT '审批人ID',
    approve_time TIMESTAMP COMMENT '审批时间',
    reject_reason VARCHAR(500) COMMENT '拒绝原因',

    -- 有效期
    expires_at TIMESTAMP NOT NULL COMMENT '密钥过期时间',

    -- 审计字段
    tenant_id VARCHAR(36) NOT NULL,
    create_by VARCHAR(36) NOT NULL,
    create_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    update_by VARCHAR(36),
    update_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    deleted BOOLEAN DEFAULT FALSE,

    INDEX idx_key_hash (key_hash),
    INDEX idx_target (target_type, target_id),
    INDEX idx_status (status),
    INDEX idx_tenant_id (tenant_id),
    INDEX idx_create_by (create_by)
) COMMENT 'API密钥管理表';
```

### 2. API密钥使用记录表
```sql
CREATE TABLE sys_api_key_usage (
    id VARCHAR(36) PRIMARY KEY,
    api_key_id VARCHAR(36) NOT NULL COMMENT 'API密钥ID',

    -- 使用信息
    request_ip VARCHAR(50) COMMENT '请求IP',
    request_uri VARCHAR(200) COMMENT '请求URI',
    request_method VARCHAR(10) COMMENT '请求方法',
    request_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '请求时间',

    -- 响应信息
    response_status INT COMMENT '响应状态码',
    response_time BIGINT COMMENT '响应时间（毫秒）',

    -- 错误信息
    error_message VARCHAR(500) COMMENT '错误信息',

    INDEX idx_api_key_id (api_key_id),
    INDEX idx_request_time (request_time),
    FOREIGN KEY (api_key_id) REFERENCES sys_api_key(id)
) COMMENT 'API密钥使用记录表';
```

## 三、注解设计

### 1. API密钥认证注解
```java
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresApiKey {

    /**
     * 支持的目标类型
     */
    String[] targetTypes() default {};

    /**
     * 需要的权限
     */
    String[] permissions() default {};

    /**
     * 权限关系类型
     */
    Logical logical() default Logical.AND;

    /**
     * 认证失败时的提示信息
     */
    String message() default "API密钥认证失败";

    /**
     * 是否记录使用日志
     */
    boolean logUsage() default true;
}
```

## 四、核心服务接口

### 1. API密钥服务接口
```java
public interface ApiKeyService {

    /**
     * 生成API密钥
     */
    String generateApiKey(ApiKeyRequest request);

    /**
     * 验证API密钥
     */
    ApiKeyValidationResult validateApiKey(String compositeKey, String targetType, String targetId);

    /**
     * 审批API密钥
     */
    void approveApiKey(String keyId, String approveBy);

    /**
     * 拒绝API密钥
     */
    void rejectApiKey(String keyId, String rejectReason, String rejectBy);

    /**
     * 禁用API密钥
     */
    void disableApiKey(String keyId);

    /**
     * 记录使用情况
     */
    void recordUsage(String keyId, ApiKeyUsageRecord record);
}
```

## 五、认证流程设计

### 1. 第三方系统调用流程
1. 第三方系统获取授权密钥Key
2. 生成当前时间戳
3. 组合生成新密钥：`Base64(授权密钥Key + "." + 时间戳)`
4. 在请求头中携带新密钥：`Authorization: ApiKey <新密钥>`
5. 平台接收请求并验证密钥

### 2. 平台验证流程
1. 从请求头提取API密钥
2. Base64解码获取授权密钥Key和时间戳
3. 验证时效性（30分钟限制）
4. 验证授权密钥Key的JWT签名
5. 验证权限和访问范围
6. 记录使用日志
7. 放行请求

## 六、安全考虑

### 1. 密钥安全
- 授权密钥Key使用强加密算法生成
- 支持密钥轮换和撤销
- 敏感信息加密存储

### 2. 访问控制
- 基于IP白名单的访问控制
- 访问频率限制
- 异常访问监控和告警

### 3. 审计日志
- 完整的密钥申请、审批、使用记录
- 支持审计查询和报表生成
- 异常行为检测和告警
