package com.xhcai.modules.rag.vo;

import com.baomidou.mybatisplus.annotation.TableField;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.datasource.handler.GenericJsonbTypeHandler;
import com.xhcai.modules.rag.entity.inner.CleaningConfig;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;
import org.apache.ibatis.type.JdbcType;

import java.time.LocalDateTime;
import java.util.Map;

/**
 * 文档视图对象
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Schema(description = "文档视图对象")
public class DocumentVO {

    @Schema(description = "文档ID", example = "doc_123")
    private String id;

    @Schema(description = "知识库ID", example = "dataset_123")
    private String datasetId;

    @Schema(description = "知识库名称", example = "技术文档库")
    private String datasetName;

    @Schema(description = "文件序号", example = "1")
    private Integer position;

    @Schema(description = "上传成功时间", example = "2025-01-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime uploadTime;

    @Schema(description = "数据源类型", example = "upload_file")
    private String dataSourceType;

    @Schema(description = "数据源类型描述", example = "文件上传")
    private String dataSourceTypeDesc;

    @Schema(description = "数据源信息", example = "file_upload_123")
    private String dataSourceInfo;

    @Schema(description = "批次号", example = "batch_20231201_001")
    private String batch;

    @Schema(description = "文件名称", example = "技术文档.pdf")
    private String name;

    @Schema(description = "字符数", example = "5000")
    private Integer wordCount;

    @Schema(description = "文件大小", example = "5000")
    private Long fileSize;

    @Schema(description = "Token数", example = "1250")
    private Integer tokens;

    @Schema(description = "索引化耗时", example = "15.5")
    private Double indexingLatency;

    @Schema(description = "是否暂停", example = "false")
    private Boolean isPaused;

    @Schema(description = "暂停人ID", example = "user_456")
    private String pausedBy;

    @Schema(description = "暂停人姓名", example = "李四")
    private String pausedByName;

    @Schema(description = "暂停时间", example = "2023-12-01 14:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime pausedAt;

    @Schema(description = "异常原因", example = "文件格式不支持")
    private String error;

    @Schema(description = "文档处理状态", example = "completed")
    private String documentStatus;

    @Schema(description = "文档处理状态描述", example = "已完成")
    private String documentStatusDesc;

    @Schema(description = "文档处理状态背底色", example = "#000000")
    private String documentStatusBgColor;

    @Schema(description = "是否启用", example = "true")
    private Boolean enabled;

    @Schema(description = "是否归档", example = "false")
    private Boolean archived;

    @Schema(description = "归档原因", example = "内容过时")
    private String archivedReason;

    @Schema(description = "文档类型", example = "pdf")
    private String docType;

    @Schema(description = "文档类型描述", example = "PDF")
    private String docTypeName;

    @Schema(description = "文档图标")
    private String docIcon;

    @Schema(description = "文档样式")
    private String docStyle;

    @Schema(description = "文档元数据", example = "{\"pages\": 10, \"size\": 1024000}")
    private Map<String, Object> docMetadata;

    @Schema(description = "分段配置")
    @TableField(value = "segment_config", typeHandler = GenericJsonbTypeHandler.class, jdbcType = JdbcType.OTHER)
    private SegmentConfig segmentConfig;

    @Schema(description = "清洗配置")
    @TableField(value = "cleaning_config", typeHandler = GenericJsonbTypeHandler.class, jdbcType = JdbcType.OTHER)
    private CleaningConfig cleaningConfig;

    @Schema(description = "文档形式", example = "text_model")
    private String docForm;

    @Schema(description = "文档语言", example = "zh-CN")
    private String docLanguage;

    @Schema(description = "分类ID", example = "category_123")
    private String categoryId;

    @Schema(description = "分类名称", example = "产品文档")
    private String categoryName;

    @Schema(description = "分类详细信息")
    private DocumentCategoryVO category;

    @Schema(description = "创建人ID", example = "user_123")
    private String createBy;

    @Schema(description = "创建人姓名", example = "张三")
    private String createByName;

    @Schema(description = "创建时间", example = "2023-12-01 10:00:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime createTime;

    @Schema(description = "处理开始时间", example = "2023-12-01 10:05:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime processingStartedAt;

    @Schema(description = "解析完成时间", example = "2023-12-01 10:10:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime parsingCompletedAt;

    @Schema(description = "清理完成时间", example = "2023-12-01 10:12:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime cleaningCompletedAt;

    @Schema(description = "分段完成时间", example = "2023-12-01 10:15:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime splittingCompletedAt;

    @Schema(description = "完成时间", example = "2023-12-01 10:20:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime completedAt;

    @Schema(description = "更新人")
    private String updateBy;

    @Schema(description = "上传人")
    private String uploadBy;

    @Schema(description = "更新时间", example = "2023-12-01 15:30:00")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    private LocalDateTime updateTime;

    @Schema(description = "分段数量", example = "15")
    private Long segmentCount;

    @Schema(description = "已完成分段数", example = "15")
    private Long completedSegmentCount;

    @Schema(description = "处理中分段数", example = "0")
    private Long processingSegmentCount;

    @Schema(description = "错误分段数", example = "0")
    private Long errorSegmentCount;

    @Schema(description = "处理进度百分比", example = "100.0")
    private Double progressPercentage;
}
