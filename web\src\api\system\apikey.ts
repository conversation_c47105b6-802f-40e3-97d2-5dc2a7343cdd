import apiClient from '@/utils/apiClient'
import type { ApiResponse } from '@/types/api'

// API密钥申请数据类型
export interface ApiKeyApplyData {
  keyName: string
  applicantUnit: string
  applicantName: string
  applicantPhone: string
  responsibleUnit: string
  responsibleName: string
  responsiblePhone: string
  targetType: string
  targetId: string
  targetName: string
  accessFrequency: string
  accessCountLimit?: number
  expiresAt: string
}

// API密钥信息类型
export interface ApiKeyInfo {
  id: string
  keyName: string
  keyValue?: string
  keyHash: string
  applicantUnit: string
  applicantName: string
  applicantPhone: string
  responsibleUnit: string
  responsibleName: string
  responsiblePhone: string
  targetType: string
  targetId: string
  targetName: string
  accessFrequency: string
  accessCountLimit: number
  accessCountUsed: number
  status: string
  approveBy?: string
  approveTime?: string
  rejectReason?: string
  expiresAt: string
  tenantId: string
  createBy: string
  createTime: string
  updateBy?: string
  updateTime?: string
}

// 分页查询参数
export interface ApiKeyQueryParams {
  pageNum?: number
  pageSize?: number
  status?: string
  targetType?: string
  createBy?: string
}

// 申请API密钥
export function applyApiKey(data: ApiKeyApplyData) {
  return apiClient.post('/system/apikey/apply', data)
}

// 获取用户的API密钥列表
export function getUserApiKeys(params: ApiKeyQueryParams) {
  return apiClient.get('/system/apikey/user',params)
}

// 获取待审批的API密钥列表
export function getPendingApiKeys(params: ApiKeyQueryParams) {
  return apiClient.get('/system/apikey/pending',
    params
  )
}

// 审批API密钥
export function approveApiKey(id: string, approved: boolean, rejectReason?: string) {
  return apiClient.put(`/system/apikey/approve/${id}`,{
      approved,
      rejectReason
    }
  )
}

// 批量审批API密钥
export function batchApproveApiKey(ids: string[], approved: boolean, rejectReason?: string) {
  return apiClient.put('/system/apikey/batch-approve',{
      ids,
      approved,
      rejectReason
    }
  )
}

// 禁用API密钥
export function disableApiKey(id: string) {
  return apiClient.put(`/system/apikey/disable/${id}`)
}

// 启用API密钥
export function enableApiKey(id: string) {
  return apiClient.put(`/system/apikey/enable/${id}`)
}

// 删除API密钥
export function deleteApiKey(id: string) {
  return apiClient.delete(`/system/apikey/${id}`)
}

// 获取API密钥详情
export function getApiKeyInfo(id: string) {
  return apiClient.get( `/system/apikey/${id}`)
}

// 统计用户API密钥数量
export function countUserApiKeys(targetType?: string, status?: string) {
  return apiClient.get('/system/apikey/count', {
      targetType,
      status
    }
  )
}