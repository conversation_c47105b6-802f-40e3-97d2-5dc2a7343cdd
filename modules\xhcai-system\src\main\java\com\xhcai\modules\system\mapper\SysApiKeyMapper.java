package com.xhcai.modules.system.mapper;

import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.system.entity.SysApiKey;

/**
 * API密钥管理Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysApiKeyMapper extends BaseMapper<SysApiKey> {

    /**
     * 根据密钥哈希值查找API密钥
     *
     * @param keyHash 密钥哈希值
     * @return API密钥信息
     */
    SysApiKey selectByKeyHash(@Param("keyHash") String keyHash);

    /**
     * 根据目标类型和目标ID查找API密钥列表
     *
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @return API密钥列表
     */
    List<SysApiKey> selectByTarget(@Param("targetType") String targetType, @Param("targetId") String targetId);

    /**
     * 分页查询用户的API密钥申请
     *
     * @param page 分页参数
     * @param createBy 创建人ID
     * @param status 状态
     * @param targetType 目标类型
     * @return 分页结果
     */
    IPage<SysApiKey> selectUserApiKeys(Page<SysApiKey> page,
                                      @Param("createBy") String createBy,
                                      @Param("status") String status,
                                      @Param("targetType") String targetType);

    /**
     * 分页查询待审批的API密钥申请
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param status 状态
     * @param targetType 目标类型
     * @return 分页结果
     */
    IPage<SysApiKey> selectPendingApiKeys(Page<SysApiKey> page,
                                         @Param("tenantId") String tenantId,
                                         @Param("status") String status,
                                         @Param("targetType") String targetType);

    /**
     * 更新API密钥使用次数
     *
     * @param id API密钥ID
     * @param increment 增量
     * @return 更新行数
     */
    int updateUsageCount(@Param("id") String id, @Param("increment") int increment);

    /**
     * 批量更新API密钥状态
     *
     * @param ids API密钥ID列表
     * @param status 新状态
     * @param approveBy 审批人ID
     * @return 更新行数
     */
    int batchUpdateStatus(@Param("ids") List<String> ids,
                         @Param("status") String status,
                         @Param("approveBy") String approveBy);

    /**
     * 查询即将过期的API密钥
     *
     * @param days 提前天数
     * @return API密钥列表
     */
    List<SysApiKey> selectExpiringApiKeys(@Param("days") int days);

    /**
     * 统计用户的API密钥数量
     *
     * @param createBy 创建人ID
     * @param targetType 目标类型
     * @param status 状态
     * @return 数量
     */
    int countUserApiKeys(@Param("createBy") String createBy,
                        @Param("targetType") String targetType,
                        @Param("status") String status);
}