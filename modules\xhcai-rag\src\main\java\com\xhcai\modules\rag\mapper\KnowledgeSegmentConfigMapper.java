package com.xhcai.modules.rag.mapper;

import com.xhcai.common.datasource.handler.GenericJsonbTypeHandler;
import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Result;
import org.apache.ibatis.annotations.Results;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.type.JdbcType;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.xhcai.modules.rag.entity.KnowledgeSegmentConfig;

/**
 * 知识库分段配置Mapper
 *
 * <AUTHOR>
 */
@Mapper
public interface KnowledgeSegmentConfigMapper extends BaseMapper<KnowledgeSegmentConfig> {

    /**
     * 根据租户ID获取配置
     */
    @Results(id = "KnowledgeSegmentConfigResultMap", value = {
        @Result(column = "id", property = "id"),
        @Result(column = "tenant_id", property = "tenantId"),
        @Result(column = "segment_config", property = "segmentConfig",
                typeHandler = GenericJsonbTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "cleaning_config", property = "cleaningConfig",
                typeHandler = GenericJsonbTypeHandler.class, jdbcType = JdbcType.OTHER),
        @Result(column = "remark", property = "remark"),
        @Result(column = "deleted", property = "deleted"),
        @Result(column = "created_by", property = "createdBy"),
        @Result(column = "created_at", property = "createdAt"),
        @Result(column = "updated_by", property = "updatedBy"),
        @Result(column = "updated_at", property = "updatedAt")
    })
    @Select("SELECT * FROM datasets_segment_config WHERE tenant_id = #{tenantId} AND deleted = 0 LIMIT 1")
    KnowledgeSegmentConfig getByTenantId(String tenantId);
}
