package com.xhcai.modules.rag.handler;

import com.xhcai.common.datasource.handler.GenericJsonbTypeHandler;
import com.xhcai.modules.rag.entity.inner.CleaningConfig;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * JSONB -> CleaningConfig 的类型处理器（基于通用处理器并指定目标类型）。
 */
@MappedTypes(CleaningConfig.class)
@MappedJdbcTypes(JdbcType.OTHER)
public class CleaningConfigJsonbTypeHandler extends GenericJsonbTypeHandler<CleaningConfig> {
    public CleaningConfigJsonbTypeHandler() {
        super(CleaningConfig.class);
    }
}

