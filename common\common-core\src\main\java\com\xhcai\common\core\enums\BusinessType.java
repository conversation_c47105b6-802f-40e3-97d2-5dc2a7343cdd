package com.xhcai.common.core.enums;

/**
 * 业务操作类型枚举
 * 用于操作日志记录中标识不同的业务操作类型
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public enum BusinessType {

    /**
     * 其它
     */
    OTHER("OTHER", "其它"),

    /**
     * 新增
     */
    INSERT("INSERT", "新增"),

    /**
     * 修改
     */
    UPDATE("UPDATE", "修改"),

    /**
     * 删除
     */
    DELETE("DELETE", "删除"),

    /**
     * 授权
     */
    GRANT("GRANT", "授权"),

    /**
     * 导出
     */
    EXPORT("EXPORT", "导出"),

    /**
     * 导入
     */
    IMPORT("IMPORT", "导入"),

    /**
     * 强退
     */
    FORCE("FORCE", "强退"),

    /**
     * 生成代码
     */
    GENCODE("GENCODE", "生成代码"),

    /**
     * 清空数据
     */
    CLEAN("CLEAN", "清空数据"),

    /**
     * 审批
     */
    APPROVE("APPROVE", "审批"),

    /**
     * 拒绝
     */
    REJECT("REJECT", "拒绝"),

    /**
     * 启用
     */
    ENABLE("ENABLE", "启用"),

    /**
     * 禁用
     */
    DISABLE("DISABLE", "禁用"),

    /**
     * 重置
     */
    RESET("RESET", "重置"),

    /**
     * 上传
     */
    UPLOAD("UPLOAD", "上传"),

    /**
     * 下载
     */
    DOWNLOAD("DOWNLOAD", "下载"),

    /**
     * 发布
     */
    PUBLISH("PUBLISH", "发布"),

    /**
     * 撤销
     */
    REVOKE("REVOKE", "撤销"),

    /**
     * 同步
     */
    SYNC("SYNC", "同步"),

    /**
     * 备份
     */
    BACKUP("BACKUP", "备份"),

    /**
     * 恢复
     */
    RESTORE("RESTORE", "恢复");

    /**
     * 业务类型代码
     */
    private final String code;

    /**
     * 业务类型描述
     */
    private final String description;

    BusinessType(String code, String description) {
        this.code = code;
        this.description = description;
    }

    /**
     * 获取业务类型代码
     *
     * @return 业务类型代码
     */
    public String getCode() {
        return code;
    }

    /**
     * 获取业务类型描述
     *
     * @return 业务类型描述
     */
    public String getDescription() {
        return description;
    }

    /**
     * 根据代码获取业务类型
     *
     * @param code 业务类型代码
     * @return 业务类型枚举
     */
    public static BusinessType fromCode(String code) {
        for (BusinessType type : BusinessType.values()) {
            if (type.getCode().equals(code)) {
                return type;
            }
        }
        return OTHER;
    }

    @Override
    public String toString() {
        return this.description;
    }
}
