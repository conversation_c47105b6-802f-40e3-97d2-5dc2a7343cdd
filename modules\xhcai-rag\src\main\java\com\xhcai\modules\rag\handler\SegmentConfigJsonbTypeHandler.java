package com.xhcai.modules.rag.handler;

import com.xhcai.common.datasource.handler.GenericJsonbTypeHandler;
import com.xhcai.modules.rag.entity.inner.SegmentConfig;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;

/**
 * JSONB -> SegmentConfig 的类型处理器（基于通用处理器指定目标类型）。
 */
@MappedTypes(SegmentConfig.class)
@MappedJdbcTypes(JdbcType.OTHER)
public class SegmentConfigJsonbTypeHandler extends GenericJsonbTypeHandler<SegmentConfig> {
    public SegmentConfigJsonbTypeHandler() {
        super(SegmentConfig.class);
    }
}

