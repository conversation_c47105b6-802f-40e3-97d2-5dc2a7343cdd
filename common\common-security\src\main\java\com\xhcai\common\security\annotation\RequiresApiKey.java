package com.xhcai.common.security.annotation;

import java.lang.annotation.*;

/**
 * API密钥认证注解
 * 用于方法级别的API密钥认证控制，支持第三方业务系统通过密钥Key访问指定接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.METHOD, ElementType.TYPE})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface RequiresApiKey {

    /**
     * 支持的目标类型
     * 限制API密钥只能访问指定类型的资源
     */
    String[] targetTypes() default {};

    /**
     * 需要的权限
     * 支持多个权限，默认为AND关系
     */
    String[] permissions() default {};

    /**
     * 权限关系类型
     * AND: 需要拥有所有权限
     * OR: 需要拥有任意一个权限
     */
    Logical logical() default Logical.AND;

    /**
     * 认证失败时的提示信息
     */
    String message() default "API密钥认证失败";

    /**
     * 是否记录使用日志
     * 默认记录所有API密钥的使用情况
     */
    boolean logUsage() default true;

    /**
     * 是否允许同时使用JWT Token认证
     * 如果为true，则同时支持JWT Token和API密钥认证
     * 如果为false，则只支持API密钥认证
     */
    boolean allowTokenAuth() default true;

    /**
     * 逻辑关系枚举
     */
    enum Logical {
        /**
         * 且关系
         */
        AND,
        /**
         * 或关系
         */
        OR
    }
}