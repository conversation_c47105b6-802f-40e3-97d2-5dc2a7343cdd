package com.xhcai.modules.system.dto;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.validation.constraints.NotBlank;
import lombok.Data;

import java.util.List;

@Data
public class SysApiKeyApproveDTO {

    @Schema(description = "API密钥ID列表", requiredMode = Schema.RequiredMode.REQUIRED)
    private List<String> ids;

    @Schema(description = "审批状态", requiredMode = Schema.RequiredMode.REQUIRED)
    @NotBlank(message = "审批状态不能为空")
    private boolean approved;

    @Schema(description = "拒绝原因")
    private String rejectReason;
}
