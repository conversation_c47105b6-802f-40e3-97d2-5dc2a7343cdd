package com.xhcai.modules.system.mapper;

import java.time.LocalDateTime;
import java.util.List;

import org.apache.ibatis.annotations.Mapper;
import org.apache.ibatis.annotations.Param;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.modules.system.entity.SysApiKeyUsage;

/**
 * API密钥使用记录Mapper接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Mapper
public interface SysApiKeyUsageMapper extends BaseMapper<SysApiKeyUsage> {

    /**
     * 分页查询API密钥使用记录
     *
     * @param page 分页参数
     * @param apiKeyId API密钥ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @param responseStatus 响应状态码
     * @return 分页结果
     */
    IPage<SysApiKeyUsage> selectUsageRecords(Page<SysApiKeyUsage> page,
                                            @Param("apiKeyId") String apiKeyId,
                                            @Param("startTime") LocalDateTime startTime,
                                            @Param("endTime") LocalDateTime endTime,
                                            @Param("responseStatus") Integer responseStatus);

    /**
     * 统计API密钥使用次数
     *
     * @param apiKeyId API密钥ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 使用次数
     */
    int countUsageByApiKey(@Param("apiKeyId") String apiKeyId,
                          @Param("startTime") LocalDateTime startTime,
                          @Param("endTime") LocalDateTime endTime);

    /**
     * 统计API密钥成功调用次数
     *
     * @param apiKeyId API密钥ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 成功次数
     */
    int countSuccessUsageByApiKey(@Param("apiKeyId") String apiKeyId,
                                 @Param("startTime") LocalDateTime startTime,
                                 @Param("endTime") LocalDateTime endTime);

    /**
     * 查询API密钥最近使用记录
     *
     * @param apiKeyId API密钥ID
     * @param limit 限制数量
     * @return 使用记录列表
     */
    List<SysApiKeyUsage> selectRecentUsage(@Param("apiKeyId") String apiKeyId, @Param("limit") int limit);

    /**
     * 删除过期的使用记录
     *
     * @param beforeTime 时间点
     * @return 删除行数
     */
    int deleteExpiredRecords(@Param("beforeTime") LocalDateTime beforeTime);

    /**
     * 统计API密钥使用情况汇总
     *
     * @param apiKeyId API密钥ID
     * @param startTime 开始时间
     * @param endTime 结束时间
     * @return 使用统计信息
     */
    List<Object> selectUsageStatistics(@Param("apiKeyId") String apiKeyId,
                                      @Param("startTime") LocalDateTime startTime,
                                      @Param("endTime") LocalDateTime endTime);
}