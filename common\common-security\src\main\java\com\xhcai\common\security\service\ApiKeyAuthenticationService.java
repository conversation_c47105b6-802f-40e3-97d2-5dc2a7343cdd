package com.xhcai.common.security.service;

import org.springframework.security.core.userdetails.UserDetails;

import com.xhcai.common.security.annotation.RequiresApiKey;

import java.time.LocalDateTime;

/**
 * API密钥认证服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ApiKeyAuthenticationService {

    /**
     * 验证API密钥并返回用户详情
     *
     * @param compositeApiKey 复合API密钥（包含时间戳）
     * @param targetTypes 允许的目标类型
     * @param permissions 需要的权限
     * @param logical 权限逻辑关系
     * @param requestUri 请求URI
     * @return 用户详情
     * @throws com.xhcai.common.core.exception.BusinessException 认证失败时抛出异常
     */
    UserDetails authenticateApiKey(String compositeApiKey, String[] targetTypes,
                                 String[] permissions, RequiresApiKey.Logical logical,
                                 String requestUri);

    /**
     * 生成API密钥
     *
     * @param userId 用户ID
     * @param username 用户名
     * @param tenantId 租户ID
     * @param deptId 部门ID
     * @param targetType 目标类型
     * @param targetId 目标ID
     * @param expiresAt 过期天数
     * @return JWT格式的API密钥
     */
    String generateApiKey(String userId, String username, String tenantId, String deptId,
                         String targetType, String targetId, LocalDateTime expiresAt);

    /**
     * 生成密钥哈希值
     *
     * @param apiKey 原始API密钥
     * @return SHA-256哈希值
     */
    String generateKeyHash(String apiKey);

    /**
     * 验证复合API密钥的时效性
     *
     * @param compositeApiKey 复合API密钥
     * @return 原始API密钥
     * @throws com.xhcai.common.core.exception.BusinessException 验证失败时抛出异常
     */
    String validateCompositeApiKey(String compositeApiKey);

    /**
     * 创建复合API密钥
     * 第三方业务系统使用此方法将原始密钥与当前时间组合
     *
     * @param originalApiKey 原始API密钥
     * @param timestamp 当前时间戳
     * @return Base64编码的复合密钥
     */
    String createCompositeApiKey(String originalApiKey, long timestamp);
}