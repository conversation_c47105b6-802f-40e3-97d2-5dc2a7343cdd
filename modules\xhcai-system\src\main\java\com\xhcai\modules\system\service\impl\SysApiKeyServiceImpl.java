package com.xhcai.modules.system.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.security.service.ApiKeyAuthenticationService;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.entity.SysApiKey;
import com.xhcai.modules.system.enums.ApiKeyStatus;
import com.xhcai.modules.system.mapper.SysApiKeyMapper;
import com.xhcai.modules.system.service.ISysApiKeyService;
import com.xhcai.modules.system.vo.SysApiKeyVO;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.StringUtils;

import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.List;
import java.util.stream.Collectors;

/**
 * API密钥管理服务实现类
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Slf4j
@Service
public class SysApiKeyServiceImpl extends ServiceImpl<SysApiKeyMapper, SysApiKey> implements ISysApiKeyService {

    @Autowired
    private ApiKeyAuthenticationService apiKeyAuthenticationService;

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean applyApiKey(SysApiKey apiKey) {
        try {
            // 设置申请信息
            apiKey.setStatus(ApiKeyStatus.PENDING.getCode()); // 待审批

            // 设置默认值
            if (apiKey.getAccessCountLimit() == null) {
                apiKey.setAccessCountLimit(1000); // 默认1000次
            }
            return save(apiKey);
        } catch (Exception e) {
            log.error("申请API密钥失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean approveApiKey(String id, boolean approved, String rejectReason) {
        try {
            SysApiKey apiKey = getById(id);
            if (apiKey == null) {
                log.warn("API密钥不存在: {}", id);
                return false;
            }

            if (approved) {

                // 生成API密钥
                String generatedKey = apiKeyAuthenticationService.generateApiKey(
                        SecurityUtils.getCurrentUserId(),
                        SecurityUtils.getCurrentUsername(),
                        SecurityUtils.getCurrentTenantId(),
                        SecurityUtils.getCurrentUserDeptId(),
                        apiKey.getTargetType(),
                        apiKey.getTargetId(),
                        apiKey.getExpiresAt()
                );

                apiKey.setKeyValue(generatedKey);
                apiKey.setKeyHash(apiKeyAuthenticationService.generateKeyHash(generatedKey));
                apiKey.setStatus(ApiKeyStatus.APPROVED.getCode());
                apiKey.setApproveTime(LocalDateTime.now());
                apiKey.setApproveBy(SecurityUtils.getCurrentUserId());
                
                // 设置过期时间
                apiKey.setExpiresAt(apiKey.getExpiresAt());
            } else {
                apiKey.setStatus(ApiKeyStatus.REJECTED.getCode());
                apiKey.setRejectReason(rejectReason);
                apiKey.setApproveTime(LocalDateTime.now());
                apiKey.setApproveBy(SecurityUtils.getCurrentUserId());
            }

            return updateById(apiKey);
        } catch (Exception e) {
            log.error("审批API密钥失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean batchApproveApiKey(List<String> ids, boolean approved, String rejectReason) {
        try {
            for (String id : ids) {
                if (!approveApiKey(id, approved, rejectReason)) {
                    log.warn("批量审批API密钥失败，ID: {}", id);
                    return false;
                }
            }
            return true;
        } catch (Exception e) {
            log.error("批量审批API密钥失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean disableApiKey(String id) {
        try {
            SysApiKey apiKey = getById(id);
            if (apiKey == null) {
                return false;
            }
            
            apiKey.setStatus(ApiKeyStatus.DISABLED.getCode());
            return updateById(apiKey);
        } catch (Exception e) {
            log.error("禁用API密钥失败", e);
            return false;
        }
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean enableApiKey(String id) {
        try {
            SysApiKey apiKey = getById(id);
            if (apiKey == null) {
                return false;
            }
            
            apiKey.setStatus(ApiKeyStatus.APPROVED.getCode());
            return updateById(apiKey);
        } catch (Exception e) {
            log.error("启用API密钥失败", e);
            return false;
        }
    }

    @Override
    public SysApiKey getByKeyHash(String keyHash) {
        if (!StringUtils.hasText(keyHash)) {
            return null;
        }
        
        LambdaQueryWrapper<SysApiKey> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysApiKey::getKeyHash, keyHash)
               .eq(SysApiKey::getStatus, ApiKeyStatus.APPROVED.getCode());
        
        return getOne(wrapper);
    }

    @Override
    public PageResult<SysApiKeyVO> getUserApiKeys(Page<SysApiKey> page, String createBy, String status, String targetType) {
        LambdaQueryWrapper<SysApiKey> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysApiKey::getCreateBy, createBy);
        
        if (StringUtils.hasText(status)) {
            wrapper.eq(SysApiKey::getStatus, status);
        }
        if (StringUtils.hasText(targetType)) {
            wrapper.eq(SysApiKey::getTargetType, targetType);
        }
        
        wrapper.orderByDesc(SysApiKey::getCreateTime);

        Page<SysApiKey> apiKeys = page(page, wrapper);

        List<SysApiKeyVO> list = apiKeys.getRecords().stream().map(apiKey -> {
            SysApiKeyVO sysApiKeyVO = new SysApiKeyVO();
            BeanUtils.copyProperties(apiKey, sysApiKeyVO);
            return sysApiKeyVO;
        }).toList();

        return PageResult.of(list, apiKeys.getTotal(), apiKeys.getCurrent(), apiKeys.getSize());
    }

    @Override
    public PageResult<SysApiKeyVO> getPendingApiKeys(Page<SysApiKey> page, String tenantId, String status, String targetType) {
        LambdaQueryWrapper<SysApiKey> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysApiKey::getTenantId, tenantId);
        
        if (StringUtils.hasText(status)) {
            wrapper.eq(SysApiKey::getStatus, status);
        }
        
        if (StringUtils.hasText(targetType)) {
            wrapper.eq(SysApiKey::getTargetType, targetType);
        }
        
        wrapper.orderByAsc(SysApiKey::getCreateTime);

        Page<SysApiKey> apiKeys = page(page, wrapper);

        List<SysApiKeyVO> list = apiKeys.getRecords().stream().map(apiKey -> {
            SysApiKeyVO sysApiKeyVO = new SysApiKeyVO();
            BeanUtils.copyProperties(apiKey, sysApiKeyVO);
            return sysApiKeyVO;
        }).toList();
        
        return PageResult.of(list, apiKeys.getTotal(), apiKeys.getCurrent(), apiKeys.getSize());
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public boolean incrementUsageCount(String id, int increment) {
        try {
            SysApiKey apiKey = getById(id);
            if (apiKey == null) {
                return false;
            }
            
            int currentCount = apiKey.getAccessCountUsed() != null ? apiKey.getAccessCountUsed() : 0;
            apiKey.setAccessCountUsed(currentCount + increment);
            apiKey.setUpdateTime(LocalDateTime.now());
            
            return updateById(apiKey);
        } catch (Exception e) {
            log.error("增加API密钥使用次数失败", e);
            return false;
        }
    }

    @Override
    public boolean isUsageLimitExceeded(String id) {
        SysApiKey apiKey = getById(id);
        if (apiKey == null) {
            return true; // 不存在的密钥视为超限
        }

        Integer usageCount = apiKey.getAccessCountUsed();
        Integer accessCountLimit = apiKey.getAccessCountLimit();

        if (usageCount == null || accessCountLimit == null) {
            return false;
        }

        return usageCount >= accessCountLimit;
    }

    @Override
    public List<SysApiKey> getExpiringApiKeys(int days) {
        LambdaQueryWrapper<SysApiKey> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysApiKey::getStatus, ApiKeyStatus.APPROVED.getCode())
               .le(SysApiKey::getExpiresAt, LocalDateTime.now().plusDays(days))
               .gt(SysApiKey::getExpiresAt, LocalDateTime.now());
        
        return list(wrapper);
    }

    @Override
    public int countUserApiKeys(String createBy, String targetType, String status) {
        LambdaQueryWrapper<SysApiKey> wrapper = new LambdaQueryWrapper<>();
        wrapper.eq(SysApiKey::getCreateBy, createBy);
        
        if (StringUtils.hasText(targetType)) {
            wrapper.eq(SysApiKey::getTargetType, targetType);
        }
        if (StringUtils.hasText(status)) {
            wrapper.eq(SysApiKey::getStatus, status);
        }
        
        return Math.toIntExact(count(wrapper));
    }
}
