package com.xhcai.modules.system.service;

import java.util.List;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.extension.service.IService;
import com.xhcai.common.api.response.PageResult;
import com.xhcai.modules.system.entity.SysApiKey;
import com.xhcai.modules.system.vo.SysApiKeyVO;

/**
 * API密钥管理服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ISysApiKeyService extends IService<SysApiKey> {

    /**
     * 申请API密钥
     *
     * @param apiKey API密钥申请信息
     * @return 申请结果
     */
    boolean applyApiKey(SysApiKey apiKey);

    /**
     * 审批API密钥
     *
     * @param id API密钥ID
     * @param approved 是否通过
     * @param rejectReason 拒绝原因（审批不通过时）
     * @return 审批结果
     */
    boolean approveApiKey(String id, boolean approved, String rejectReason);

    /**
     * 批量审批API密钥
     *
     * @param ids API密钥ID列表
     * @param approved 是否通过
     * @param rejectReason 拒绝原因（审批不通过时）
     * @return 审批结果
     */
    boolean batchApproveApiKey(List<String> ids, boolean approved, String rejectReason);

    /**
     * 禁用API密钥
     *
     * @param id API密钥ID
     * @return 禁用结果
     */
    boolean disableApiKey(String id);

    /**
     * 启用API密钥
     *
     * @param id API密钥ID
     * @return 启用结果
     */
    boolean enableApiKey(String id);

    /**
     * 根据密钥哈希值查找API密钥
     *
     * @param keyHash 密钥哈希值
     * @return API密钥信息
     */
    SysApiKey getByKeyHash(String keyHash);

    /**
     * 分页查询用户的API密钥申请
     *
     * @param page 分页参数
     * @param createBy 创建人ID
     * @param status 状态
     * @param targetType 目标类型
     * @return 分页结果
     */
    PageResult<SysApiKeyVO> getUserApiKeys(Page<SysApiKey> page, String createBy, String status, String targetType);

    /**
     * 分页查询待审批的API密钥申请
     *
     * @param page 分页参数
     * @param tenantId 租户ID
     * @param status 状态
     * @param targetType 目标类型
     * @return 分页结果
     */
    PageResult<SysApiKeyVO> getPendingApiKeys(Page<SysApiKey> page, String tenantId, String status, String targetType);

    /**
     * 增加API密钥使用次数
     *
     * @param id API密钥ID
     * @param increment 增量
     * @return 更新结果
     */
    boolean incrementUsageCount(String id, int increment);

    /**
     * 检查API密钥使用次数是否超限
     *
     * @param id API密钥ID
     * @return 是否超限
     */
    boolean isUsageLimitExceeded(String id);

    /**
     * 查询即将过期的API密钥
     *
     * @param days 提前天数
     * @return API密钥列表
     */
    List<SysApiKey> getExpiringApiKeys(int days);

    /**
     * 统计用户的API密钥数量
     *
     * @param createBy 创建人ID
     * @param targetType 目标类型
     * @param status 状态
     * @return 数量
     */
    int countUserApiKeys(String createBy, String targetType, String status);
}