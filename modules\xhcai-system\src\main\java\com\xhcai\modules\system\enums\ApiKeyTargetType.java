package com.xhcai.modules.system.enums;

import lombok.Getter;

/**
 *  密钥访问对象类型枚举
 */
@Getter
public enum ApiKeyTargetType {
    AGENT("agent", "智能体", 1),
    KNOWLEDGE("knowledge", "知识库", 2),
    GRAPH("graph", "知识图谱", 3),
    MODEL("model", "模型", 4);

    private final String code;
    private final String info;
    private final int sort;

    ApiKeyTargetType(String code, String info, int sort) {
        this.code = code;
        this.info = info;
        this.sort = sort;
    }

    /**
     * 根据代码获取枚举
     */
    public static ApiKeyTargetType getByCode(String code) {
        for (ApiKeyTargetType value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
