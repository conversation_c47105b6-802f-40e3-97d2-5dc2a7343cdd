<template>
  <div class="profile-page min-h-screen flex">
    <!-- 左侧固定区域 -->
    <div class="profile-sidebar w-80 bg-white shadow-lg border-r border-gray-200 flex flex-col">
      <!-- 页面标题 -->
      <div class="page-header p-6 border-b border-gray-200">
        <h1 class="text-2xl font-bold text-gray-900">个人信息</h1>
        <p class="text-sm text-gray-600 mt-2">管理您的个人资料、账户设置和第三方智能体账号</p>
      </div>

      <!-- 个人信息导航 -->
      <div class="profile-nav flex-1 p-4">
        <div class="nav-tabs space-y-2">
          <router-link
            v-for="tab in profileTabs"
            :key="tab.key"
            :to="{ name: tab.routeName }"
            class="nav-tab w-full px-4 py-3 text-sm font-medium rounded-lg transition-all duration-300 flex items-center text-left"
            :class="{
              'bg-blue-50 text-blue-600 border border-blue-200': $route.name === tab.routeName,
              'text-gray-700 hover:text-blue-600 hover:bg-blue-50': $route.name !== tab.routeName
            }"
          >
            <span class="mr-3 text-lg">{{ tab.icon }}</span>
            <span>{{ tab.name }}</span>
          </router-link>
        </div>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="profile-content flex-1 min-h-0 overflow-auto">
      <div class="p-6">
        <div class="profile-section">
          <router-view />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
// 个人信息标签页
const profileTabs = [
  { key: 'basic', name: '基本信息', icon: '👤', routeName: 'ProfileBasic' },
  { key: 'statistics', name: '使用统计', icon: '📊', routeName: 'ProfileStatistics' },
  { key: 'security', name: '安全设置', icon: '🔒', routeName: 'ProfileSecurity' },
  { key: 'activities', name: '最近活动', icon: '⏰', routeName: 'ProfileActivities' },
  { key: 'api-keys', name: 'API密钥管理', icon: '🔑', routeName: 'ProfileApiKeys' },
  { key: 'third-platform-agents', name: '第三方智能体账号', icon: '🔗', routeName: 'ProfileThirdPlatform' }
]
</script>

<style scoped>
.profile-page {
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  min-height: 100vh;
}

.profile-sidebar {
  position: fixed;
  left: 0;
  top: 70px; /* 顶部导航栏高度 */
  height: calc(100vh - 70px); /* 减去顶部导航栏高度 */
  z-index: 40; /* 低于顶部导航栏的z-index: 50 */
}

.profile-content {
  margin-left: 320px; /* 左侧边栏宽度 + 间距 */
  height: calc(100vh - 70px); /* 减去顶部导航栏高度 */
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(10px);
  box-shadow: -4px 0 20px rgba(0, 0, 0, 0.1);
}

.nav-tab {
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.nav-tab::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(59, 130, 246, 0.1), transparent);
  transition: left 0.5s ease;
}

.nav-tab:hover::before {
  left: 100%;
}

.nav-tab:hover {
  transform: translateX(4px);
  box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
}

.nav-tab.bg-blue-50 {
  background: linear-gradient(135deg, #dbeafe 0%, #bfdbfe 100%);
  border-left: 4px solid #3b82f6;
}

.profile-section {
  animation: slideInRight 0.4s ease-out;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .profile-sidebar {
    position: relative;
    width: 100%;
    height: auto;
    top: 0;
  }

  .profile-content {
    margin-left: 0;
    margin-top: 0;
    height: auto;
    border-radius: 0;
  }

  .profile-page {
    flex-direction: column;
  }

  .nav-tabs {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 0.5rem;
  }
}

@media (max-width: 768px) {
  .nav-tabs {
    grid-template-columns: 1fr;
  }

  .page-header h1 {
    font-size: 1.5rem;
  }

  .nav-tab {
    padding: 0.75rem 1rem;
  }
}
</style>
