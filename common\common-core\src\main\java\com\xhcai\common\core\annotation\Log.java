package com.xhcai.common.core.annotation;

import com.xhcai.common.core.enums.BusinessType;

import java.lang.annotation.*;

/**
 * 自定义操作日志记录注解
 * 用于标记需要记录操作日志的方法
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Target({ElementType.PARAMETER, ElementType.METHOD})
@Retention(RetentionPolicy.RUNTIME)
@Documented
public @interface Log {

    /**
     * 模块名称
     */
    String title() default "";

    /**
     * 功能描述
     */
    String description() default "";

    /**
     * 业务操作类型
     */
    BusinessType businessType() default BusinessType.OTHER;

    /**
     * 操作人类别
     */
    OperatorType operatorType() default OperatorType.MANAGE;

    /**
     * 是否保存请求的参数
     */
    boolean isSaveRequestData() default true;

    /**
     * 是否保存响应的参数
     */
    boolean isSaveResponseData() default true;

    /**
     * 排除指定的请求参数
     */
    String[] excludeParamNames() default {};

    /**
     * 操作人类别枚举
     */
    enum OperatorType {
        /**
         * 其它
         */
        OTHER,

        /**
         * 后台用户
         */
        MANAGE,

        /**
         * 手机端用户
         */
        MOBILE,

        /**
         * API用户
         */
        API
    }
}
