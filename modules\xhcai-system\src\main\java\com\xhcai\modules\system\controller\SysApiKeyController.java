package com.xhcai.modules.system.controller;

import java.util.List;

import com.xhcai.common.api.response.PageResult;
import com.xhcai.common.api.response.Result;
import com.xhcai.common.core.annotation.Log;
import com.xhcai.common.core.enums.BusinessType;
import com.xhcai.common.security.utils.SecurityUtils;
import com.xhcai.modules.system.dto.SysApiKeyApproveDTO;
import com.xhcai.modules.system.vo.SysApiKeyVO;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.DeleteMapping;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import com.baomidou.mybatisplus.core.metadata.IPage;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.xhcai.common.security.annotation.RequiresPermissions;
import com.xhcai.modules.system.entity.SysApiKey;
import com.xhcai.modules.system.service.ISysApiKeyService;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;
import jakarta.validation.Valid;

/**
 * API密钥管理控制器
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "API密钥管理", description = "API密钥申请、审批、管理相关接口")
@RestController
@RequestMapping("/system/apikey")
@Validated
public class SysApiKeyController {

    @Autowired
    private ISysApiKeyService apiKeyService;

    /**
     * 申请API密钥
     */
    @Operation(summary = "申请API密钥", description = "用户申请API密钥访问权限")
    @PostMapping("/apply")
    public Result<String> apply(@Valid @RequestBody SysApiKey apiKey) {
        boolean result = apiKeyService.applyApiKey(apiKey);
        return result ? Result.success("申请成功，等待审批") : Result.error("申请失败");
    }

    /**
     * 审批API密钥
     */
    @Operation(summary = "审批API密钥", description = "管理员审批API密钥申请")
    @PutMapping("/approve/{id}")
    @RequiresPermissions("system:apikey:approve")
    public Result<String> approve(
            @Parameter(description = "API密钥ID") @PathVariable String id,
            @Parameter(description = "审批提交信息") @RequestBody SysApiKeyApproveDTO apiKeyApproveDTO) {

        if (!apiKeyApproveDTO.isApproved() && StringUtils.isBlank(apiKeyApproveDTO.getRejectReason())) {
            return Result.error("请填写拒绝原因");
        }

        boolean result = apiKeyService.approveApiKey(id, apiKeyApproveDTO.isApproved(), apiKeyApproveDTO.getRejectReason());
        return result ? Result.success(apiKeyApproveDTO.isApproved() ? "审批通过" : "审批拒绝") : Result.error("审批失败");
    }

    /**
     * 批量审批API密钥
     */
    @Operation(summary = "批量审批API密钥", description = "管理员批量审批API密钥申请")
    @PutMapping("/batch-approve")
    @RequiresPermissions("system:apikey:approve")
    public Result<String> batchApprove(@Parameter(description = "审批提交信息") @RequestBody SysApiKeyApproveDTO apiKeyApproveDTO) {
        if (apiKeyApproveDTO.getIds() == null || apiKeyApproveDTO.getIds().isEmpty()) {
            return Result.error("请选择要审批的API密钥");
        }
        if (!apiKeyApproveDTO.isApproved() && StringUtils.isBlank(apiKeyApproveDTO.getRejectReason())) {
            return Result.error("请填写拒绝原因");
        }
        boolean result = apiKeyService.batchApproveApiKey(apiKeyApproveDTO.getIds(), apiKeyApproveDTO.isApproved(), apiKeyApproveDTO.getRejectReason());
        return result ? Result.success("批量审批成功") : Result.error("批量审批失败");
    }

    /**
     * 禁用API密钥
     */
    @Operation(summary = "禁用API密钥", description = "禁用指定的API密钥")
    @PutMapping("/disable/{id}")
    @RequiresPermissions("system:apikey:edit")
    public Result<String> disable(@Parameter(description = "API密钥ID") @PathVariable String id) {
        boolean result = apiKeyService.disableApiKey(id);
        return result ? Result.success("禁用成功") : Result.error("禁用失败");
    }

    /**
     * 启用API密钥
     */
    @Operation(summary = "启用API密钥", description = "启用指定的API密钥")
    @PutMapping("/enable/{id}")
    @RequiresPermissions("system:apikey:edit")
    public Result<String> enable(@Parameter(description = "API密钥ID") @PathVariable String id) {
        boolean result = apiKeyService.enableApiKey(id);
        return result ? Result.success("启用成功") : Result.error("启用失败");
    }

    /**
     * 删除API密钥
     */
    @Operation(summary = "删除API密钥", description = "删除指定的API密钥")
    @DeleteMapping("/{id}")
    @RequiresPermissions("system:apikey:remove")
    public Result<String> remove(@Parameter(description = "API密钥ID") @PathVariable String id) {
        boolean result = apiKeyService.removeById(id);
        return result ? Result.success("删除成功") : Result.error("删除失败");
    }

    /**
     * 获取API密钥详情
     */
    @Operation(summary = "获取API密钥详情", description = "根据ID获取API密钥详细信息")
    @GetMapping("/{id}")
    @RequiresPermissions("system:apikey:query")
    public Result<SysApiKey> getInfo(@Parameter(description = "API密钥ID") @PathVariable String id) {
        SysApiKey apiKey = apiKeyService.getById(id);
        return Result.success(apiKey);
    }

    /**
     * 分页查询用户的API密钥申请
     */
    @Operation(summary = "查询用户API密钥", description = "分页查询当前用户的API密钥申请")
    @GetMapping("/user")
    public Result<PageResult<SysApiKeyVO>> getUserApiKeys(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "状态") @RequestParam(required = false) String status,
            @Parameter(description = "目标类型") @RequestParam(required = false) String targetType) {
        Page<SysApiKey> page = new Page<>(pageNum, pageSize);
        String createBy = SecurityUtils.getCurrentUserId();
        PageResult<SysApiKeyVO> result = apiKeyService.getUserApiKeys(page, createBy, status, targetType);

        return Result.success(result);
    }

    /**
     * 分页查询待审批的API密钥申请
     */
    @Operation(summary = "查询待审批API密钥", description = "分页查询待审批的API密钥申请")
    @GetMapping("/pending")
    @RequiresPermissions("system:apikey:approve")
    public Result<PageResult<SysApiKeyVO>> getPendingApiKeys(
            @Parameter(description = "页码") @RequestParam(defaultValue = "1") int pageNum,
            @Parameter(description = "页大小") @RequestParam(defaultValue = "10") int pageSize,
            @Parameter(description = "状态") @RequestParam(required = false) String status,
            @Parameter(description = "目标类型") @RequestParam(required = false) String targetType) {
        Page<SysApiKey> page = new Page<>(pageNum, pageSize);
        String tenantId = SecurityUtils.getCurrentTenantId();
        PageResult<SysApiKeyVO> result = apiKeyService.getPendingApiKeys(page, tenantId, status, targetType);
        return Result.success(result);
    }

    /**
     * 查询即将过期的API密钥
     */
    @Operation(summary = "查询即将过期的API密钥", description = "查询即将过期的API密钥列表")
    @GetMapping("/expiring")
    @RequiresPermissions("system:apikey:query")
    public Result<List<SysApiKey>> getExpiringApiKeys(
            @Parameter(description = "提前天数") @RequestParam(defaultValue = "7") int days) {
        List<SysApiKey> result = apiKeyService.getExpiringApiKeys(days);
        return Result.success(result);
    }

    /**
     * 统计用户的API密钥数量
     */
    @Operation(summary = "统计用户API密钥数量", description = "统计当前用户的API密钥数量")
    @GetMapping("/count")
    public Result<Integer> countUserApiKeys(
            @Parameter(description = "目标类型") @RequestParam(required = false) String targetType,
            @Parameter(description = "状态") @RequestParam(required = false) String status) {
        String createBy = SecurityUtils.getCurrentUserId();
        int count = apiKeyService.countUserApiKeys(createBy, targetType, status);
        return Result.success(count);
    }
}