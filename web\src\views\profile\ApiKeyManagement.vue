<template>
  <div class="api-key-management">
    <!-- 页面标题 -->
    <div class="section-header">
      <h2 class="text-2xl font-bold text-gray-900 mb-2">
        <el-icon class="text-blue-500 text-3xl"><Key /></el-icon>
        API密钥管理
      </h2>
      <p class="text-gray-600">管理您申请的API密钥，查看审批状态和使用情况</p>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
      <div class="stat-card bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-blue-600 text-sm font-medium">总申请数</p>
            <p class="text-2xl font-bold text-blue-800">{{ stats.total }}</p>
          </div>
          <el-icon class="text-blue-500 text-3xl"><Document /></el-icon>
        </div>
      </div>

      <div class="stat-card bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-green-600 text-sm font-medium">已通过</p>
            <p class="text-2xl font-bold text-green-800">{{ stats.approved }}</p>
          </div>
          <el-icon class="text-green-500 text-3xl"><CircleCheck /></el-icon>
        </div>
      </div>

      <div class="stat-card bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-xl p-6 border border-yellow-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-yellow-600 text-sm font-medium">审核中</p>
            <p class="text-2xl font-bold text-yellow-800">{{ stats.pending }}</p>
          </div>
          <el-icon class="text-yellow-500 text-3xl"><Clock /></el-icon>
        </div>
      </div>

      <div class="stat-card bg-gradient-to-r from-red-50 to-red-100 rounded-xl p-6 border border-red-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-red-600 text-sm font-medium">已拒绝</p>
            <p class="text-2xl font-bold text-red-800">{{ stats.rejected }}</p>
          </div>
          <el-icon class="text-red-500 text-3xl"><CircleClose /></el-icon>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
      <div class="flex flex-col md:flex-row gap-4 items-center justify-between">
        <div class="flex flex-col md:flex-row gap-4 flex-1">
          <el-select v-model="filters.status" placeholder="选择状态" clearable class="w-full md:w-48">
            <el-option label="全部状态" value="" />
            <el-option label="审核中" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已禁用" value="disabled" />
          </el-select>

          <el-select v-model="filters.targetType" placeholder="选择类型" clearable class="w-full md:w-48">
            <el-option label="全部类型" value="" />
            <el-option label="智能体" value="agent" />
            <el-option label="知识库" value="knowledge" />
            <el-option label="知识图谱" value="graph" />
            <el-option label="模型" value="model" />
          </el-select>

          <el-input
            v-model="filters.keyword"
            placeholder="搜索密钥名称或目标名称"
            clearable
            class="w-full md:w-64"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>

        <el-button type="primary" @click="loadApiKeys" :loading="loading">
          <el-icon><Refresh /></el-icon>
          刷新
        </el-button>
      </div>
    </div>

    <!-- API密钥列表 -->
    <div class="api-key-list bg-white rounded-xl shadow-sm border border-gray-200">
      <!-- 加载状态 -->
      <div v-if="loading" class="p-8 text-center">
        <el-icon class="text-4xl text-gray-400 animate-spin"><Loading /></el-icon>
        <p class="text-gray-500 mt-2">加载中...</p>
      </div>

      <!-- 空状态 -->
      <div v-else-if="filteredApiKeys.length === 0" class="p-8 text-center">
        <el-icon class="text-6xl text-gray-300"><Key /></el-icon>
        <p class="text-gray-500 mt-4">暂无API密钥申请记录</p>
        <p class="text-gray-400 text-sm mt-2">您可以在智能体或知识库页面申请API密钥</p>
      </div>

      <!-- 密钥列表 -->
      <div v-else class="divide-y divide-gray-200">
        <div
          v-for="apiKey in paginatedApiKeys"
          :key="apiKey.id"
          class="p-6 hover:bg-gray-50 transition-colors duration-200"
        >
          <div class="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-4">
            <!-- 左侧信息 -->
            <div class="flex-1">
              <div class="flex items-center gap-3 mb-2">
                <h3 class="text-lg font-semibold text-gray-800">{{ apiKey.keyName }}</h3>
                <el-tag :type="getStatusTagType(apiKey.status)" size="small">
                  {{ getStatusText(apiKey.status) }}
                </el-tag>
                <el-tag type="info" size="small">
                  {{ getTargetTypeText(apiKey.targetType) }}
                </el-tag>
              </div>

              <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 text-sm text-gray-600">
                <div>
                  <span class="font-medium">目标名称:</span>
                  <span class="ml-1">{{ apiKey.targetName }}</span>
                </div>
                <div>
                  <span class="font-medium">申请单位:</span>
                  <span class="ml-1">{{ apiKey.applicantUnit }}</span>
                </div>
                <div>
                  <span class="font-medium">申请人:</span>
                  <span class="ml-1">{{ apiKey.applicantName }}</span>
                </div>
                <div>
                  <span class="font-medium">访问频率:</span>
                  <span class="ml-1">{{ getFrequencyText(apiKey.accessFrequency) }}</span>
                </div>
                <div>
                  <span class="font-medium">使用次数:</span>
                  <span class="ml-1">{{ apiKey.accessCountUsed || 0 }}/{{ apiKey.accessCountLimit || '无限制' }}</span>
                </div>
                <div>
                  <span class="font-medium">申请时间:</span>
                  <span class="ml-1">{{ formatDate(apiKey.createTime) }}</span>
                </div>
              </div>

              <!-- 审批信息 -->
              <div v-if="apiKey.status === 'approved' && apiKey.approveTime" class="mt-2 text-sm text-green-600">
                <span class="font-medium">审批时间:</span>
                <span class="ml-1">{{ formatDate(apiKey.approveTime) }}</span>
                <span v-if="apiKey.approveBy" class="ml-2">
                  <span class="font-medium">审批人:</span>
                  <span class="ml-1">{{ apiKey.approveBy }}</span>
                </span>
              </div>

              <div v-if="apiKey.status === 'rejected' && apiKey.rejectReason" class="mt-2 text-sm text-red-600">
                <span class="font-medium">拒绝原因:</span>
                <span class="ml-1">{{ apiKey.rejectReason }}</span>
              </div>
            </div>

            <!-- 右侧操作 -->
            <div class="flex items-center gap-2">
              <el-button
                v-if="apiKey.status === 'approved' && apiKey.keyValue"
                type="primary"
                size="small"
                @click="showApiKey(apiKey)"
              >
                <el-icon><View /></el-icon>
                查看密钥
              </el-button>

              <el-button
                v-if="apiKey.status === 'approved'"
                type="success"
                size="small"
                @click="showUsageStats(apiKey)"
              >
                <el-icon><DataAnalysis /></el-icon>
                使用统计
              </el-button>

              <el-button
                v-if="apiKey.status === 'approved'"
                type="warning"
                size="small"
                @click="disableApiKeyHandler(apiKey)"
              >
                <el-icon><Lock /></el-icon>
                禁用
              </el-button>
            </div>
          </div>
        </div>
      </div>

      <!-- 分页 -->
      <div v-if="filteredApiKeys.length > 0" class="p-6 border-t border-gray-200">
        <el-pagination
          v-model:current-page="pagination.current"
          v-model:page-size="pagination.size"
          :total="filteredApiKeys.length"
          :page-sizes="[10, 20, 50, 100]"
          layout="total, sizes, prev, pager, next, jumper"
          class="justify-center"
        />
      </div>
    </div>

    <!-- 查看密钥对话框 -->
    <el-dialog
      v-model="keyDialog.visible"
      title="API密钥详情"
      width="600px"
      :before-close="closeKeyDialog"
    >
      <div v-if="keyDialog.apiKey" class="space-y-4">
        <div class="bg-gray-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-2">
            <span class="text-sm font-medium text-gray-600">API密钥</span>
            <el-button
              type="primary"
              size="small"
              @click="copyApiKey"
            >
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
          </div>
          <div class="font-mono text-sm bg-white p-3 rounded border break-all">
            {{ keyDialog.showKey ? keyDialog.apiKey.keyValue : '••••••••••••••••••••••••••••••••' }}
          </div>
          <div class="mt-2">
            <el-button
              type="text"
              size="small"
              @click="keyDialog.showKey = !keyDialog.showKey"
            >
              <el-icon><View /></el-icon>
              {{ keyDialog.showKey ? '隐藏' : '显示' }}密钥
            </el-button>
          </div>
        </div>

        <div class="bg-blue-50 rounded-lg p-4">
          <h4 class="text-sm font-medium text-blue-800 mb-2">使用说明</h4>
          <div class="text-sm text-blue-700 space-y-1">
            <p>1. 请妥善保管您的API密钥，不要泄露给他人</p>
            <p>2. 使用时需要将密钥与当前时间戳组合后进行Base64编码</p>
            <p>3. 密钥有效期为1年，过期后需要重新申请</p>
            <p>4. 如发现密钥泄露，请立即禁用并重新申请</p>
          </div>
        </div>
      </div>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Key,
  Document,
  CircleCheck,
  Clock,
  CircleClose,
  Search,
  Refresh,
  Loading,
  View,
  DataAnalysis,
  Lock,
  CopyDocument
} from '@element-plus/icons-vue'
import {
  getUserApiKeys,
  disableApiKey,
  getApiKeyInfo,
  countUserApiKeys,
  type ApiKeyInfo,
  type ApiKeyQueryParams
} from '@/api/system/apikey'

// 响应式数据
const loading = ref(false)
const apiKeys = ref<ApiKeyInfo[]>([])

// 统计数据
const stats = ref({
  total: 0,
  approved: 0,
  pending: 0,
  rejected: 0
})

// 筛选条件
const filters = ref({
  status: '',
  targetType: '',
  keyword: ''
})

// 分页
const pagination = ref({
  current: 1,
  size: 10
})

// 密钥详情对话框
const keyDialog = ref({
  visible: false,
  apiKey: null as ApiKeyInfo | null,
  showKey: false
})

// 计算属性
const filteredApiKeys = computed(() => {
  let result = apiKeys.value

  // 状态筛选
  if (filters.value.status) {
    result = result.filter(key => key.status === filters.value.status)
  }

  // 类型筛选
  if (filters.value.targetType) {
    result = result.filter(key => key.targetType === filters.value.targetType)
  }

  // 关键词搜索
  if (filters.value.keyword) {
    const keyword = filters.value.keyword.toLowerCase()
    result = result.filter(key =>
      key.keyName.toLowerCase().includes(keyword) ||
      key.targetName.toLowerCase().includes(keyword)
    )
  }

  return result
})

const paginatedApiKeys = computed(() => {
  const start = (pagination.value.current - 1) * pagination.value.size
  const end = start + pagination.value.size
  return filteredApiKeys.value.slice(start, end)
})

// 方法
const loadApiKeys = async () => {
  try {
    loading.value = true
    const response = await getUserApiKeys({
      pageNum: 1,
      pageSize: 30 // 获取所有数据，前端分页
    })

    if (response.code === 200) {
      apiKeys.value = response.data.records
      updateStats()
    } else {
      ElMessage.error(response.message || '加载API密钥列表失败')
    }
  } catch (error) {
    console.error('加载API密钥列表失败:', error)
    ElMessage.error('加载API密钥列表失败')
  } finally {
    loading.value = false
  }
}

const updateStats = () => {
  stats.value.total = apiKeys.value.length
  stats.value.approved = apiKeys.value.filter(key => key.status === 'approved').length
  stats.value.pending = apiKeys.value.filter(key => key.status === 'pending').length
  stats.value.rejected = apiKeys.value.filter(key => key.status === 'rejected').length
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '审核中'
    case 'approved': return '已通过'
    case 'rejected': return '已拒绝'
    case 'disabled': return '已禁用'
    default: return status
  }
}

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'pending': return 'warning'
    case 'approved': return 'success'
    case 'rejected': return 'danger'
    case 'disabled': return 'info'
    default: return 'info'
  }
}

const getTargetTypeText = (targetType: string) => {
  switch (targetType) {
    case 'agent': return '智能体'
    case 'knowledge': return '知识库'
    case 'graph': return '知识图谱'
    case 'model': return '模型'
    default: return targetType
  }
}

const getFrequencyText = (frequency: string) => {
  switch (frequency) {
    case 'low': return '低频'
    case 'medium': return '中频'
    case 'high': return '高频'
    default: return frequency
  }
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

const showApiKey = async (apiKey: ApiKeyInfo) => {
  try {
    // 获取完整的密钥信息（包含keyValue）
    const response = await getApiKeyInfo(apiKey.id)
    if (response.code === 200) {
      keyDialog.value.apiKey = response.data
      keyDialog.value.visible = true
      keyDialog.value.showKey = false
    } else {
      ElMessage.error(response.message || '获取密钥详情失败')
    }
  } catch (error) {
    console.error('获取密钥详情失败:', error)
    ElMessage.error('获取密钥详情失败')
  }
}

const closeKeyDialog = () => {
  keyDialog.value.visible = false
  keyDialog.value.apiKey = null
  keyDialog.value.showKey = false
}

const copyApiKey = async () => {
  if (!keyDialog.value.apiKey?.keyValue) return

  try {
    await navigator.clipboard.writeText(keyDialog.value.apiKey.keyValue)
    ElMessage.success('API密钥已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

const showUsageStats = (apiKey: ApiKeyInfo) => {
  ElMessage.info('使用统计功能开发中...')
}

const disableApiKeyHandler = async (apiKey: ApiKeyInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要禁用密钥"${apiKey.keyName}"吗？禁用后将无法使用此密钥访问API。`,
      '确认禁用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await disableApiKey(apiKey.id)
    if (response.code === 200) {
      ElMessage.success('密钥已禁用')
      await loadApiKeys() // 重新加载列表
    } else {
      ElMessage.error(response.message || '禁用失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('禁用密钥失败:', error)
      ElMessage.error('禁用密钥失败')
    }
  }
}

// 监听筛选条件变化，重置分页
watch(filters, () => {
  pagination.value.current = 1
}, { deep: true })

// 初始化
onMounted(() => {
  loadApiKeys()
})
</script>

<style scoped>
.api-key-management {
  padding: 0;
}

.section-header {
  margin-bottom: 2rem;
}

.stats-grid {
  margin-bottom: 2rem;
}

.stat-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.filter-section {
  transition: all 0.3s ease;
}

.filter-section:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.api-key-list {
  transition: all 0.3s ease;
}

.api-key-list:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

/* 响应式设计 */
@media (max-width: 768px) {
  .section-header {
    padding: 1.5rem;
    text-align: center;
  }

  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .filter-section {
    padding: 1rem;
  }

  .api-key-list .p-6 {
    padding: 1rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .section-header p {
    font-size: 0.875rem;
  }
}

/* 为每个统计卡片添加延迟动画 */
.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
</style>