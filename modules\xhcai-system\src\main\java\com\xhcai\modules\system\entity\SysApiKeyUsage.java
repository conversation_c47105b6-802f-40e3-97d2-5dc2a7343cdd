package com.xhcai.modules.system.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.datasource.entity.BaseEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.Size;

/**
 * API密钥使用记录实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Entity
@Table(name = "sys_api_key_usage", indexes = {
    @Index(name = "idx_usage_api_key_id", columnList = "api_key_id"),
    @Index(name = "idx_usage_request_time", columnList = "request_time"),
    @Index(name = "idx_usage_request_ip", columnList = "request_ip"),
    @Index(name = "idx_usage_response_status", columnList = "response_status")
})
@Schema(description = "API密钥使用记录")
@TableName("sys_api_key_usage")
public class SysApiKeyUsage extends BaseEntity {

    private static final long serialVersionUID = 1L;

    /**
     * API密钥ID
     */
    @Column(name = "api_key_id", length = 36)
    @Schema(description = "API密钥ID", example = "123e4567-e89b-12d3-a456-************")
    @NotBlank(message = "API密钥ID不能为空")
    @Size(max = 36, message = "API密钥ID长度不能超过36个字符")
    @TableField("api_key_id")
    private String apiKeyId;

    /**
     * 请求IP
     */
    @Column(name = "request_ip", length = 50)
    @Schema(description = "请求IP", example = "*************")
    @Size(max = 50, message = "请求IP长度不能超过50个字符")
    @TableField("request_ip")
    private String requestIp;

    /**
     * 请求URI
     */
    @Column(name = "request_uri", length = 200)
    @Schema(description = "请求URI", example = "/api/agent/chat")
    @Size(max = 200, message = "请求URI长度不能超过200个字符")
    @TableField("request_uri")
    private String requestUri;

    /**
     * 请求方法
     */
    @Column(name = "request_method", length = 10)
    @Schema(description = "请求方法", example = "POST")
    @Size(max = 10, message = "请求方法长度不能超过10个字符")
    @TableField("request_method")
    private String requestMethod;

    /**
     * 请求时间
     */
    @Column(name = "request_time")
    @Schema(description = "请求时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("request_time")
    private LocalDateTime requestTime;

    /**
     * 请求头信息（JSON格式）
     */
    @Column(name = "request_headers", columnDefinition = "TEXT")
    @Schema(description = "请求头信息（JSON格式）")
    @TableField("request_headers")
    private String requestHeaders;

    /**
     * 响应状态码
     */
    @Column(name = "response_status")
    @Schema(description = "响应状态码", example = "200")
    @TableField("response_status")
    private Integer responseStatus;

    /**
     * 响应时间（毫秒）
     */
    @Column(name = "response_time")
    @Schema(description = "响应时间（毫秒）", example = "150")
    @TableField("response_time")
    private Long responseTime;

    /**
     * 错误信息
     */
    @Column(name = "error_message", length = 500)
    @Schema(description = "错误信息")
    @Size(max = 500, message = "错误信息长度不能超过500个字符")
    @TableField("error_message")
    private String errorMessage;

    // Getters and Setters
    public String getApiKeyId() {
        return apiKeyId;
    }

    public void setApiKeyId(String apiKeyId) {
        this.apiKeyId = apiKeyId;
    }

    public String getRequestIp() {
        return requestIp;
    }

    public void setRequestIp(String requestIp) {
        this.requestIp = requestIp;
    }

    public String getRequestUri() {
        return requestUri;
    }

    public void setRequestUri(String requestUri) {
        this.requestUri = requestUri;
    }

    public String getRequestMethod() {
        return requestMethod;
    }

    public void setRequestMethod(String requestMethod) {
        this.requestMethod = requestMethod;
    }

    public LocalDateTime getRequestTime() {
        return requestTime;
    }

    public void setRequestTime(LocalDateTime requestTime) {
        this.requestTime = requestTime;
    }

    public String getRequestHeaders() {
        return requestHeaders;
    }

    public void setRequestHeaders(String requestHeaders) {
        this.requestHeaders = requestHeaders;
    }

    public Integer getResponseStatus() {
        return responseStatus;
    }

    public void setResponseStatus(Integer responseStatus) {
        this.responseStatus = responseStatus;
    }

    public Long getResponseTime() {
        return responseTime;
    }

    public void setResponseTime(Long responseTime) {
        this.responseTime = responseTime;
    }

    public String getErrorMessage() {
        return errorMessage;
    }

    public void setErrorMessage(String errorMessage) {
        this.errorMessage = errorMessage;
    }
}