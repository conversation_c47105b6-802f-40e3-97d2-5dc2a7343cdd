package com.xhcai.common.security.filter;

import java.io.IOException;
import java.time.LocalDateTime;
import java.util.Base64;

import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.SecurityUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.context.SecurityContextHolder;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.security.web.authentication.WebAuthenticationDetailsSource;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.web.filter.OncePerRequestFilter;
import org.springframework.web.method.HandlerMethod;
import org.springframework.web.servlet.HandlerExecutionChain;
import org.springframework.web.servlet.HandlerMapping;

import com.xhcai.common.security.annotation.RequiresApiKey;
import com.xhcai.common.security.service.ApiKeyAuthenticationService;
import com.xhcai.common.security.service.ApiKeyUsageService;

import jakarta.servlet.FilterChain;
import jakarta.servlet.ServletException;
import jakarta.servlet.http.HttpServletRequest;
import jakarta.servlet.http.HttpServletResponse;

/**
 * API密钥认证过滤器
 * 支持第三方业务系统通过密钥Key访问指定接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Component
public class ApiKeyAuthenticationFilter extends OncePerRequestFilter {

    private static final Logger logger = LoggerFactory.getLogger(ApiKeyAuthenticationFilter.class);

    private static final String AUTHORIZATION_HEADER = "Authorization";
    private static final String API_KEY_PREFIX = "ApiKey ";

    @Autowired
    private ApiKeyAuthenticationService apiKeyAuthenticationService;

    @Autowired
    private ApiKeyUsageService apiKeyUsageService;

    @Autowired
    @Qualifier("requestMappingHandlerMapping")
    private HandlerMapping handlerMapping;

    @Override
    protected void doFilterInternal(HttpServletRequest request, HttpServletResponse response, FilterChain filterChain)
            throws ServletException, IOException {

        try {
            // 检查是否需要API密钥认证
            RequiresApiKey requiresApiKey = getRequiresApiKeyAnnotation(request);
            if (requiresApiKey == null) {
                // 不需要API密钥认证，继续执行
                filterChain.doFilter(request, response);
                return;
            }

            // 提取API密钥
            String compositeApiKey = getApiKeyFromRequest(request);
            if (!StringUtils.hasText(compositeApiKey)) {
                // 检查是否允许Token认证
                if (requiresApiKey.allowTokenAuth() && SecurityContextHolder.getContext().getAuthentication() != null) {
                    // 已有Token认证，继续执行
                    filterChain.doFilter(request, response);
                    return;
                }

                // 没有API密钥且不允许Token认证
                handleAuthenticationFailure(response, "缺少API密钥", requiresApiKey.message());
                return;
            }

            // 验证API密钥
            long startTime = System.currentTimeMillis();
            try {
                UserDetails userDetails = apiKeyAuthenticationService.authenticateApiKey(
                    compositeApiKey,
                    requiresApiKey.targetTypes(),
                    requiresApiKey.permissions(),
                    requiresApiKey.logical(),
                    request.getRequestURI()
                );

                // 设置认证信息
                UsernamePasswordAuthenticationToken authentication =
                    new UsernamePasswordAuthenticationToken(userDetails, null, userDetails.getAuthorities());
                authentication.setDetails(new WebAuthenticationDetailsSource().buildDetails(request));
                SecurityContextHolder.getContext().setAuthentication(authentication);

                // 记录使用日志
                if (requiresApiKey.logUsage()) {
                    recordApiKeyUsage(request, compositeApiKey, 200, System.currentTimeMillis() - startTime, null);
                }

                filterChain.doFilter(request, response);

            } catch (BusinessException e) {
                logger.warn("API密钥认证失败: {}", e.getMessage());

                // 记录失败日志
                if (requiresApiKey.logUsage()) {
                    recordApiKeyUsage(request, compositeApiKey, 401, System.currentTimeMillis() - startTime, e.getMessage());
                }

                handleAuthenticationFailure(response, e.getMessage(), requiresApiKey.message());
            }

        } catch (Exception e) {
            logger.error("API密钥认证过滤器异常", e);
            handleAuthenticationFailure(response, "认证系统异常", "API密钥认证失败");
        }
    }

    /**
     * 获取方法上的RequiresApiKey注解
     */
    private RequiresApiKey getRequiresApiKeyAnnotation(HttpServletRequest request) {
        try {
            HandlerExecutionChain handlerChain = handlerMapping.getHandler(request);
            if (handlerChain != null && handlerChain.getHandler() instanceof HandlerMethod) {
                HandlerMethod handlerMethod = (HandlerMethod) handlerChain.getHandler();

                // 先检查方法级别的注解
                RequiresApiKey methodAnnotation = handlerMethod.getMethodAnnotation(RequiresApiKey.class);
                if (methodAnnotation != null) {
                    return methodAnnotation;
                }

                // 再检查类级别的注解
                return handlerMethod.getBeanType().getAnnotation(RequiresApiKey.class);
            }
        } catch (Exception e) {
            logger.debug("获取RequiresApiKey注解失败", e);
        }
        return null;
    }

    /**
     * 从请求中提取API密钥
     */
    private String getApiKeyFromRequest(HttpServletRequest request) {
        String authHeader = request.getHeader(AUTHORIZATION_HEADER);
        if (StringUtils.hasText(authHeader) && authHeader.startsWith(API_KEY_PREFIX)) {
            return authHeader.substring(API_KEY_PREFIX.length());
        }
        return null;
    }

    /**
     * 记录API密钥使用情况
     */
    private void recordApiKeyUsage(HttpServletRequest request, String compositeApiKey,
                                 int responseStatus, long responseTime, String errorMessage) {
        try {
            // 解析复合密钥获取原始密钥ID
            String originalApiKey = parseOriginalApiKey(compositeApiKey);
            if (originalApiKey != null) {
                apiKeyUsageService.recordUsage(
                    originalApiKey,
                    SecurityUtils.getClientIpAddress(request),
                    request.getRequestURI(),
                    request.getMethod(),
                    LocalDateTime.now(),
                    responseStatus,
                    responseTime,
                    errorMessage
                );
            }
        } catch (Exception e) {
            logger.warn("记录API密钥使用情况失败", e);
        }
    }

    /**
     * 从复合密钥中解析出原始API密钥
     */
    private String parseOriginalApiKey(String compositeApiKey) {
        try {
            String decoded = new String(Base64.getDecoder().decode(compositeApiKey));
            int dotIndex = decoded.lastIndexOf('.');
            if (dotIndex > 0) {
                return decoded.substring(0, dotIndex);
            }
        } catch (Exception e) {
            logger.debug("解析复合密钥失败", e);
        }
        return null;
    }

    /**
     * 处理认证失败
     */
    private void handleAuthenticationFailure(HttpServletResponse response, String error, String message)
            throws IOException {
        response.setStatus(HttpServletResponse.SC_UNAUTHORIZED);
        response.setContentType("application/json;charset=UTF-8");
        response.getWriter().write(String.format(
            "{\"code\":401,\"message\":\"%s\",\"data\":null}",
            StringUtils.hasText(message) ? message : error
        ));
    }
}