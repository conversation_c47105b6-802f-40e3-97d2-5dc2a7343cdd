package com.xhcai.modules.system.enums;

import lombok.Getter;

/**
 * 访问频率字典数据
 */
@Getter
public enum ApiKeyFrequency {

    LOW("low", "低频（每分钟10次以下）", 1),
    MEDIUM("medium", "中频（每分钟10-50次）", 2),
    HIGH("high", "高频（每分钟50次以上）", 3);

    private final String code;
    private final String info;
    private final int sort;

    ApiKeyFrequency(String code, String info, int sort) {
        this.code = code;
        this.info = info;
        this.sort = sort;
    }

    /**
     * 根据代码获取枚举
     */
    public static ApiKeyFrequency getByCode(String code) {
        for (ApiKeyFrequency value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
