/* eslint-disable */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
// biome-ignore lint: disable
export {}

/* prettier-ignore */
declare module 'vue' {
  export interface GlobalComponents {
    AssociateThirdPlatform: typeof import('./src/components/agent/AssociateThirdPlatform.vue')['default']
    DeptTreeSelector: typeof import('./src/components/common/selectors/DeptTreeSelector.vue')['default']
    DeptTreeSelectorContent: typeof import('./src/components/common/selectors/DeptTreeSelectorContent.vue')['default']
    DocumentImportDialog: typeof import('./src/components/DocumentImportDialog.vue')['default']
    DropdownSelector: typeof import('./src/components/common/selectors/DropdownSelector.vue')['default']
    ElAlert: typeof import('element-plus/es')['ElAlert']
    ElAvatar: typeof import('element-plus/es')['ElAvatar']
    ElButton: typeof import('element-plus/es')['ElButton']
    ElCheckbox: typeof import('element-plus/es')['ElCheckbox']
    ElDatePicker: typeof import('element-plus/es')['ElDatePicker']
    ElDeptTreeSelector: typeof import('./src/components/common/el-selectors/ElDeptTreeSelector.vue')['default']
    ElDescriptions: typeof import('element-plus/es')['ElDescriptions']
    ElDescriptionsItem: typeof import('element-plus/es')['ElDescriptionsItem']
    ElDialog: typeof import('element-plus/es')['ElDialog']
    ElDivider: typeof import('element-plus/es')['ElDivider']
    ElDropdown: typeof import('element-plus/es')['ElDropdown']
    ElDropdownItem: typeof import('element-plus/es')['ElDropdownItem']
    ElDropdownMenu: typeof import('element-plus/es')['ElDropdownMenu']
    ElEmpty: typeof import('element-plus/es')['ElEmpty']
    ElForm: typeof import('element-plus/es')['ElForm']
    ElFormItem: typeof import('element-plus/es')['ElFormItem']
    ElIcon: typeof import('element-plus/es')['ElIcon']
    ElImage: typeof import('element-plus/es')['ElImage']
    ElInput: typeof import('element-plus/es')['ElInput']
    ElInputNumber: typeof import('element-plus/es')['ElInputNumber']
    ElLoadingSpinner: typeof import('element-plus/es')['ElLoadingSpinner']
    ElOption: typeof import('element-plus/es')['ElOption']
    ElPagination: typeof import('element-plus/es')['ElPagination']
    ElPermissionByRoleSelector: typeof import('./src/components/common/el-selectors/ElPermissionByRoleSelector.vue')['default']
    ElProgress: typeof import('element-plus/es')['ElProgress']
    ElRadio: typeof import('element-plus/es')['ElRadio']
    ElRadioGroup: typeof import('element-plus/es')['ElRadioGroup']
    ElRoleSelector: typeof import('./src/components/common/el-selectors/ElRoleSelector.vue')['default']
    ElSelect: typeof import('element-plus/es')['ElSelect']
    ElSlider: typeof import('element-plus/es')['ElSlider']
    ElStatusSelector: typeof import('./src/components/common/el-selectors/ElStatusSelector.vue')['default']
    ElSwitch: typeof import('element-plus/es')['ElSwitch']
    ElTable: typeof import('element-plus/es')['ElTable']
    ElTableColumn: typeof import('element-plus/es')['ElTableColumn']
    ElTabPane: typeof import('element-plus/es')['ElTabPane']
    ElTabs: typeof import('element-plus/es')['ElTabs']
    ElTag: typeof import('element-plus/es')['ElTag']
    ElTreeSelect: typeof import('element-plus/es')['ElTreeSelect']
    ElUpload: typeof import('element-plus/es')['ElUpload']
    ElUserByDeptSelector: typeof import('./src/components/common/el-selectors/ElUserByDeptSelector.vue')['default']
    ElUserByRoleSelector: typeof import('./src/components/common/el-selectors/ElUserByRoleSelector.vue')['default']
    FileUploadMenu: typeof import('./src/components/FileUploadMenu.vue')['default']
    HelpModal: typeof import('./src/components/HelpModal.vue')['default']
    HoverTooltip: typeof import('./src/components/common/HoverTooltip.vue')['default']
    IconSelector: typeof import('./src/components/common/IconSelector.vue')['default']
    Pagination: typeof import('./src/components/common/Pagination.vue')['default']
    ParameterConfig: typeof import('./src/components/ParameterConfig.vue')['default']
    PermissionByRoleSelector: typeof import('./src/components/common/selectors/PermissionByRoleSelector.vue')['default']
    PermissionByRoleSelectorContent: typeof import('./src/components/common/selectors/PermissionByRoleSelectorContent.vue')['default']
    RendererSelector: typeof import('./src/components/RendererSelector.vue')['default']
    RichTextEditor: typeof import('./src/components/RichTextEditor.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
    RunnerTaskbar: typeof import('./src/components/common/runner-window/RunnerTaskbar.vue')['default']
    RunnerWindow: typeof import('./src/components/common/runner-window/RunnerWindow.vue')['default']
    StatusSelector: typeof import('./src/components/common/selectors/StatusSelector.vue')['default']
    StatusSelectorContent: typeof import('./src/components/common/selectors/StatusSelectorContent.vue')['default']
    SuggestedQuestions: typeof import('./src/components/SuggestedQuestions.vue')['default']
    UserByDeptSelector: typeof import('./src/components/common/selectors/UserByDeptSelector.vue')['default']
    UserByDeptSelectorContent: typeof import('./src/components/common/selectors/UserByDeptSelectorContent.vue')['default']
    UserByRoleSelector: typeof import('./src/components/common/selectors/UserByRoleSelector.vue')['default']
    UserByRoleSelectorContent: typeof import('./src/components/common/selectors/UserByRoleSelectorContent.vue')['default']
  }
  export interface GlobalDirectives {
    vLoading: typeof import('element-plus/es')['ElLoadingDirective']
  }
}
