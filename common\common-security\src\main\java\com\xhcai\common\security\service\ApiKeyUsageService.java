package com.xhcai.common.security.service;

import java.time.LocalDateTime;

/**
 * API密钥使用记录服务接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
public interface ApiKeyUsageService {

    /**
     * 记录API密钥使用情况
     *
     * @param apiKeyId API密钥ID
     * @param requestIp 请求IP
     * @param requestUri 请求URI
     * @param requestMethod 请求方法
     * @param requestTime 请求时间
     * @param responseStatus 响应状态码
     * @param responseTime 响应时间（毫秒）
     * @param errorMessage 错误信息
     */
    void recordUsage(String apiKeyId, String requestIp, String requestUri, String requestMethod,
                    LocalDateTime requestTime, Integer responseStatus, Long responseTime, String errorMessage);

    /**
     * 增加API密钥使用次数
     *
     * @param apiKeyId API密钥ID
     */
    void incrementUsageCount(String apiKeyId);

    /**
     * 检查API密钥使用次数是否超限
     *
     * @param apiKeyId API密钥ID
     * @return 是否超限
     */
    boolean isUsageLimitExceeded(String apiKeyId);
}