package com.xhcai.modules.system.controller;

import com.xhcai.common.api.response.Result;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.xhcai.common.security.annotation.RequiresApiKey;

import io.swagger.v3.oas.annotations.Operation;
import io.swagger.v3.oas.annotations.Parameter;
import io.swagger.v3.oas.annotations.tags.Tag;

/**
 * API密钥认证演示控制器
 * 展示如何使用@RequiresApiKey注解保护接口
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Tag(name = "API密钥认证演示", description = "演示API密钥认证功能的示例接口")
@RestController
@RequestMapping("/api/demo")
public class ApiDemoController {

    /**
     * 智能体聊天接口 - 需要智能体类型的API密钥
     */
    @Operation(summary = "智能体聊天", description = "与指定智能体进行对话")
    @PostMapping("/agent/{agentId}/chat")
    @RequiresApiKey(
        targetTypes = {"agent"},
        permissions = {"agent:chat"},
        message = "需要智能体API密钥才能访问此接口"
    )
    public Result<String> agentChat(
            @Parameter(description = "智能体ID") @PathVariable String agentId,
            @Parameter(description = "聊天消息") @RequestBody String message) {
        return Result.success("智能体回复: " + message);
    }

    /**
     * 知识库查询接口 - 需要知识库类型的API密钥
     */
    @Operation(summary = "知识库查询", description = "查询指定知识库内容")
    @GetMapping("/knowledge/{knowledgeId}/search")
    @RequiresApiKey(
        targetTypes = {"knowledge"},
        permissions = {"knowledge:search"},
        message = "需要知识库API密钥才能访问此接口"
    )
    public Result<String> knowledgeSearch(
            @Parameter(description = "知识库ID") @PathVariable String knowledgeId,
            @Parameter(description = "查询关键词") String keyword) {
        return Result.success("知识库查询结果: " + keyword);
    }

    /**
     * 知识图谱查询接口 - 需要知识图谱类型的API密钥
     */
    @Operation(summary = "知识图谱查询", description = "查询指定知识图谱")
    @GetMapping("/graph/{graphId}/query")
    @RequiresApiKey(
        targetTypes = {"graph"},
        permissions = {"graph:query"},
        message = "需要知识图谱API密钥才能访问此接口"
    )
    public Result<String> graphQuery(
            @Parameter(description = "知识图谱ID") @PathVariable String graphId,
            @Parameter(description = "查询语句") String query) {
        return Result.success("知识图谱查询结果: " + query);
    }

    /**
     * 模型推理接口 - 需要模型类型的API密钥
     */
    @Operation(summary = "模型推理", description = "使用指定模型进行推理")
    @PostMapping("/model/{modelId}/inference")
    @RequiresApiKey(
        targetTypes = {"model"},
        permissions = {"model:inference"},
        message = "需要模型API密钥才能访问此接口"
    )
    public Result<String> modelInference(
            @Parameter(description = "模型ID") @PathVariable String modelId,
            @Parameter(description = "推理数据") @RequestBody Object data) {
        return Result.success("模型推理结果: " + data.toString());
    }

    /**
     * 通用接口 - 支持多种类型的API密钥
     */
    @Operation(summary = "通用查询", description = "支持多种资源类型的通用查询接口")
    @GetMapping("/universal/{resourceId}")
    @RequiresApiKey(
        targetTypes = {"agent", "knowledge", "graph", "model"},
        permissions = {"resource:read"},
        logical = RequiresApiKey.Logical.OR,
        message = "需要相应的API密钥才能访问此接口"
    )
    public Result<String > universalQuery(
            @Parameter(description = "资源ID") @PathVariable String resourceId) {
        return Result.success("通用查询结果: " + resourceId);
    }

    /**
     * 公开接口 - 不需要API密钥认证
     */
    @Operation(summary = "公开信息", description = "不需要认证的公开接口")
    @GetMapping("/public/info")
    public Result<String> publicInfo() {
        return Result.success("这是公开信息，无需API密钥认证");
    }
}