<template>
  <el-dialog
    v-model="dialogVisible"
    title="申请API密钥"
    width="800px"
    :before-close="handleClose"
    class="api-key-dialog"
  >
    <template #header="{ titleId, titleClass }">
      <div class="dialog-header">
        <div class="kb-info" v-if="knowledgeBase">
          <div class="kb-icon" :style="{ background: knowledgeBase.iconBg || 'linear-gradient(135deg, #a8d8f0 0%, #7fb3d3 100%)' }">
            <i :class="knowledgeBase.icon || 'fas fa-database'"></i>
          </div>
          <div class="kb-details">
            <h3 :id="titleId" :class="titleClass">申请API密钥</h3>
            <span class="kb-name">{{ knowledgeBase.name }}</span>
          </div>
        </div>
      </div>
    </template>

    <div class="dialog-body">
      <el-tabs v-model="activeTab" class="api-key-tabs">
        <el-tab-pane label="申请表单" name="form">
          <div class="form-section">
            <el-form
              ref="formRef"
              :model="formData"
              :rules="formRules"
              label-width="120px"
              class="api-key-form"
            >
              <div class="form-grid">
                <el-form-item label="密钥名称" prop="keyName">
                  <el-input
                    v-model="formData.keyName"
                    placeholder="请输入密钥名称"
                    clearable
                  />
                </el-form-item>

                <el-form-item label="申请单位" prop="applicantUnit">
                  <el-input
                    v-model="formData.applicantUnit"
                    placeholder="请输入申请单位"
                    clearable
                  />
                </el-form-item>

                <el-form-item label="申请人姓名" prop="applicantName">
                  <el-input
                    v-model="formData.applicantName"
                    placeholder="请输入申请人姓名"
                    clearable
                  />
                </el-form-item>

                <el-form-item label="申请人手机号" prop="applicantPhone">
                  <el-input
                    v-model="formData.applicantPhone"
                    placeholder="请输入申请人手机号"
                    clearable
                  />
                </el-form-item>

                <el-form-item label="责任单位" prop="responsibleUnit">
                  <el-input
                    v-model="formData.responsibleUnit"
                    placeholder="请输入责任单位"
                    clearable
                  />
                </el-form-item>

                <el-form-item label="责任人姓名" prop="responsibleName">
                  <el-input
                    v-model="formData.responsibleName"
                    placeholder="请输入责任人姓名"
                    clearable
                  />
                </el-form-item>

                <el-form-item label="责任人手机号" prop="responsiblePhone">
                  <el-input
                    v-model="formData.responsiblePhone"
                    placeholder="请输入责任人手机号"
                    clearable
                  />
                </el-form-item>

                <el-form-item label="访问频率" prop="accessFrequency">
                  <el-select
                    v-model="formData.accessFrequency"
                    placeholder="请选择访问频率"
                    style="width: 100%"
                  >
                    <el-option label="低频（每分钟10次以下）" value="low" />
                    <el-option label="中频（每分钟10-50次）" value="medium" />
                    <el-option label="高频（每分钟50次以上）" value="high" />
                  </el-select>
                </el-form-item>

                <el-form-item label="预计访问次数" prop="accessCount">
                  <el-input-number
                    v-model="formData.accessCount"
                    :min="1"
                    :max="999999"
                    placeholder="请输入预计访问次数"
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item label="访问次数限制" prop="accessCountLimit">
                  <el-input-number
                    v-model="formData.accessCountLimit"
                    :min="0"
                    :max="999999"
                    placeholder="0表示无限制"
                    style="width: 100%"
                  />
                </el-form-item>

                <el-form-item label="密钥过期时间" prop="expiresAt">
                  <el-date-picker
                    v-model="formData.expiresAt"
                    type="datetime"
                    placeholder="请选择过期时间"
                    style="width: 100%"
                    :disabled-date="disabledDate"
                  />
                </el-form-item>
              </div>
            </el-form>
          </div>
        </el-tab-pane>

        <el-tab-pane label="已申请密钥" name="list">
          <div class="key-list-section">
            <div v-if="loading" class="loading-container">
              <p>正在加载...</p>
            </div>
            <div v-else-if="apiKeyList.length === 0" class="empty-state">
              <el-empty description="暂无已申请的API密钥" />
            </div>
            <div v-else class="key-items">
              <div v-for="(key, index) in currentKBApiKeys" :key="index" class="key-item">
                <div class="key-info">
                  <div class="key-header">
                    <span class="key-id">密钥ID: {{ key.id }}</span>
                    <el-tag :type="getStatusType(key.status)" size="small">
                      {{ getStatusText(key.status) }}
                    </el-tag>
                  </div>
                  <div class="key-details">
                    <el-descriptions :column="2" size="small">
                      <el-descriptions-item label="密钥名称">{{ key.keyName }}</el-descriptions-item>
                      <el-descriptions-item label="申请单位">{{ key.applicantUnit }}</el-descriptions-item>
                      <el-descriptions-item label="申请人">{{ key.applicantName }} ({{ key.applicantPhone }})</el-descriptions-item>
                      <el-descriptions-item label="责任单位">{{ key.responsibleUnit }}</el-descriptions-item>
                      <el-descriptions-item label="责任人">{{ key.responsibleName }} ({{ key.responsiblePhone }})</el-descriptions-item>
                      <el-descriptions-item label="访问频率">{{ getFrequencyText(key.accessFrequency) }}</el-descriptions-item>
                      <el-descriptions-item label="访问次数">{{ getAccessCountText(key) }}</el-descriptions-item>
                      <el-descriptions-item label="申请时间">{{ formatDateTime(key.createTime) }}</el-descriptions-item>
                    </el-descriptions>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </el-tab-pane>
      </el-tabs>
    </div>

    <template #footer>
      <div class="dialog-footer">
        <div class="footer-left">
          <span class="api-key-tip px-3">提交后将进入审核流程，请耐心等待</span>
        </div>
        <div class="footer-right">
          <el-button @click="handleClose">取消</el-button>
          <el-button
            type="primary"
            :loading="submitting"
            value-format="YYYY-MM-DD HH:mm:ss"
            @click="handleSubmit"
            v-show="activeTab === 'form'"
          >
            提交申请
          </el-button>
        </div>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { ElMessage, ElMessageBox, type FormInstance, type FormRules } from 'element-plus'
import { applyApiKey, getUserApiKeys, type ApiKeyApplyData, type ApiKeyInfo } from '@/api/system/apikey'
import { format} from 'date-fns';

// 定义组件属性
interface Props {
  visible: boolean
  knowledgeBase?: {
    id: string | number
    name: string
    icon?: string
    iconBg?: string
  } | null
}

// 定义事件
interface Emits {
  (e: 'update:visible', value: boolean): void
  (e: 'success'): void
}

const props = withDefaults(defineProps<Props>(), {
  visible: false,
  knowledgeBase: null
})

const emit = defineEmits<Emits>()

// 响应式数据
const dialogVisible = ref(false)
const activeTab = ref('form')
const loading = ref(false)
const submitting = ref(false)
const formRef = ref<FormInstance>()
const apiKeyList = ref<ApiKeyInfo[]>([])

// 表单数据
const formData = ref({
  keyName: '',
  applicantUnit: '',
  applicantName: '',
  applicantPhone: '',
  responsibleUnit: '',
  responsibleName: '',
  responsiblePhone: '',
  accessFrequency: '',
  accessCount: 1000,
  accessCountLimit: 0,
  expiresAt: ''
})

// 表单验证规则
const formRules: FormRules = {
  keyName: [
    { required: true, message: '请输入密钥名称', trigger: 'blur' }
  ],
  applicantUnit: [
    { required: true, message: '请输入申请单位', trigger: 'blur' }
  ],
  applicantName: [
    { required: true, message: '请输入申请人姓名', trigger: 'blur' }
  ],
  applicantPhone: [
    { required: true, message: '请输入申请人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  responsibleUnit: [
    { required: true, message: '请输入责任单位', trigger: 'blur' }
  ],
  responsibleName: [
    { required: true, message: '请输入责任人姓名', trigger: 'blur' }
  ],
  responsiblePhone: [
    { required: true, message: '请输入责任人手机号', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号', trigger: 'blur' }
  ],
  accessFrequency: [
    { required: true, message: '请选择访问频率', trigger: 'change' }
  ],
  accessCount: [
    { required: true, message: '请输入预计访问次数', trigger: 'blur' }
  ],
  expiresAt: [
    { required: true, message: '请选择密钥过期时间', trigger: 'change' }
  ]
}

// 计算属性
const currentKBApiKeys = computed(() => {
  if (!props.knowledgeBase) return []
  return apiKeyList.value.filter(key => key.targetId === props.knowledgeBase!.id.toString())
})

// 监听visible变化
watch(() => props.visible, (newVal) => {
  dialogVisible.value = newVal
  if (newVal) {
    resetForm()
    loadUserApiKeys()
  }
})

watch(dialogVisible, (newVal) => {
  if (!newVal) {
    emit('update:visible', false)
  }
})

// 方法
const resetForm = () => {
  activeTab.value = 'form'
  formData.value = {
    keyName: '',
    applicantUnit: '',
    applicantName: '',
    applicantPhone: '',
    responsibleUnit: '',
    responsibleName: '',
    responsiblePhone: '',
    accessFrequency: '',
    accessCount: 1000,
    accessCountLimit: 0,
    expiresAt: ''
  }
  nextTick(() => {
    formRef.value?.clearValidate()
  })
}

const handleClose = () => {
  dialogVisible.value = false
}

const disabledDate = (time: Date) => {
  return time.getTime() < Date.now() - 8.64e7 // 不能选择今天之前的日期
}

const loadUserApiKeys = async () => {
  if (!props.knowledgeBase) return
  try {
    loading.value = true
    const response = await getUserApiKeys({
      targetType: 'knowledge'
    })
    if (response.code === 200) {
      apiKeyList.value = response.data.records
    }
  } catch (error) {
    console.error('加载API密钥列表失败:', error)
    ElMessage.error('加载API密钥列表失败')
  } finally {
    loading.value = false
  }
}

const handleSubmit = async () => {
  if (!formRef.value || !props.knowledgeBase) return

  try {
    await formRef.value.validate()
    submitting.value = true

    const applyData: ApiKeyApplyData = {
      keyName: formData.value.keyName,
      applicantUnit: formData.value.applicantUnit,
      applicantName: formData.value.applicantName,
      applicantPhone: formData.value.applicantPhone,
      responsibleUnit: formData.value.responsibleUnit,
      responsibleName: formData.value.responsibleName,
      responsiblePhone: formData.value.responsiblePhone,
      targetType: 'knowledge',
      targetId: props.knowledgeBase.id.toString(),
      targetName: props.knowledgeBase.name,
      accessFrequency: formData.value.accessFrequency,
      accessCountLimit: formData.value.accessCountLimit || formData.value.accessCount || 1000,
      expiresAt: format(formData.value.expiresAt, 'yyyy-MM-dd HH:mm:ss')
    }

    const response = await applyApiKey(applyData)

    if (response.code === 200) {
      ElMessage.success('API密钥申请已提交，请等待审核')
      resetForm()
      activeTab.value = 'list'
      await loadUserApiKeys()
      emit('success')
    } else {
      ElMessage.error(response.message || 'API密钥申请失败')
    }
  } catch (error) {
    console.error('提交API密钥申请失败:', error)
    ElMessage.error('提交API密钥申请失败')
  } finally {
    submitting.value = false
  }
}

// 辅助方法
const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '审核中'
    case 'approved': return '已通过'
    case 'rejected': return '已拒绝'
    default: return status
  }
}

const getStatusType = (status: string) => {
  switch (status) {
    case 'pending': return 'warning'
    case 'approved': return 'success'
    case 'rejected': return 'danger'
    default: return 'info'
  }
}

const getFrequencyText = (frequency: string) => {
  switch (frequency) {
    case 'low': return '低频（每分钟10次以下）'
    case 'medium': return '中频（每分钟10-50次）'
    case 'high': return '高频（每分钟50次以上）'
    default: return frequency
  }
}

const getAccessCountText = (key: ApiKeyInfo) => {
  if (key.accessCountLimit === 0) return '无限制'
  return `${key.accessCountUsed || 0}/${key.accessCountLimit}`
}

const formatDateTime = (dateStr: string) => {
  if (!dateStr) return ''
  return new Date(dateStr).toLocaleString('zh-CN')
}
</script>

<style scoped>
.api-key-dialog {
  --el-dialog-border-radius: 16px;
}

.dialog-header {
  padding: 0;
}

.kb-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.kb-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 18px;
  color: #1a365d;
}

.kb-details h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e40af;
}

.kb-name {
  font-size: 12px;
  color: #4b5563;
  font-weight: normal;
}

.dialog-body {
  padding: 0;
}

.api-key-tabs {
  --el-tabs-header-height: 48px;
}

.form-section {
  padding: 20px 0;
}

.api-key-form {
  max-height: 400px;
  overflow-y: auto;
  padding-right: 10px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 16px;
}

.key-list-section {
  max-height: 400px;
  overflow-y: auto;
  padding: 20px 0;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  color: #6b7280;
}

.loading-container p {
  margin-top: 12px;
  font-size: 14px;
}

.empty-state {
  padding: 40px 20px;
}

.key-items {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.key-item {
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  padding: 16px;
  transition: all 0.2s ease;
}

.key-item:hover {
  border-color: #cbd5e1;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.key-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.key-id {
  font-weight: 600;
  color: #1e40af;
  font-size: 14px;
}

.key-details {
  margin-top: 12px;
}

.dialog-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px 0 0 0;
  border-top: 1px solid #e5e7eb;
}

.footer-left {
  flex: 1;
}

.api-key-tip {
  font-size: 12px;
  color: #6b7280;
}

.footer-right {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .form-grid {
    grid-template-columns: 1fr;
  }

  .dialog-footer {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }

  .footer-left {
    text-align: center;
  }

  .footer-right {
    justify-content: center;
  }
}
</style>
