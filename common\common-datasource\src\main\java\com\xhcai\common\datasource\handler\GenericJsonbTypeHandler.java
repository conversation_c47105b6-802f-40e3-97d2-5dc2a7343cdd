package com.xhcai.common.datasource.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;
import java.util.concurrent.ConcurrentHashMap;
import java.util.Map;

/**
 * 通用的 PostgreSQL JSONB 类型处理器，基于 Jackson 实现。
 *
 * 使用方式：
 * 在实体字段上直接使用：
 *   @TableField(value = "xxx", typeHandler = GenericJsonbTypeHandler.class, jdbcType = JdbcType.OTHER)
 * 本处理器会通过 MyBatis 传入的 javaType 构造函数自动感知目标类型，无需为每个实体单独写 Handler。
 */
@MappedTypes(Object.class) // 让注册更通用，由构造函数的 javaType 实际确定目标类型
@MappedJdbcTypes({JdbcType.OTHER})
public class GenericJsonbTypeHandler<T> extends BaseTypeHandler<T> {

    private static final Logger log = LoggerFactory.getLogger(GenericJsonbTypeHandler.class);
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

    // 缓存列名到类型的映射，提高性能
    private static final Map<String, Class<?>> COLUMN_TYPE_CACHE = new ConcurrentHashMap<>();

    // 静态初始化常用的列名到类型映射
    static {
        COLUMN_TYPE_CACHE.put("segment_config", loadClass("com.xhcai.modules.rag.entity.inner.SegmentConfig"));
        COLUMN_TYPE_CACHE.put("cleaning_config", loadClass("com.xhcai.modules.rag.entity.inner.CleaningConfig"));
        COLUMN_TYPE_CACHE.put("retrieval_settings", loadClass("com.xhcai.modules.rag.entity.KnowledgeVectorizationConfig$RetrievalSettings"));
    }

    private static Class<?> loadClass(String className) {
        try {
            return Class.forName(className);
        } catch (ClassNotFoundException e) {
            log.debug("无法加载类: {}", className);
            return Object.class;
        }
    }

    private final Class<T> type;

    /**
     * 该构造函数会被 MyBatis 在有 javaType 信息时调用（例如根据字段类型）。
     */
    @SuppressWarnings("unchecked")
    public GenericJsonbTypeHandler(Class<?> type) {
        this.type = (Class<T>) type;
    }

    /**
     * 无参构造函数：MyBatis 在 @Result 注解中使用时会调用此构造函数。
     * 此时无法获取目标类型，将在运行时动态解析。
     */
    @SuppressWarnings("unchecked")
    public GenericJsonbTypeHandler() {
        this.type = (Class<T>) Object.class; // 占位符，实际类型在运行时解析
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        try {
            PGobject jsonObject = new PGobject();
            // 使用 jsonb，保持与列定义一致
            jsonObject.setType("jsonb");

            if (parameter == null) {
                jsonObject.setValue(null);
            } else {
                String jsonString = objectMapper.writeValueAsString(parameter);
                jsonObject.setValue(jsonString);
                if (log.isDebugEnabled()) {
                    log.debug("设置JSONB参数({}): {}", type.getSimpleName(), jsonString);
                }
            }

            ps.setObject(i, jsonObject);
        } catch (JsonProcessingException e) {
            log.error("JSON 序列化失败, type={}", type, e);
            throw new SQLException("JSON 序列化失败: " + e.getMessage(), e);
        }
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String jsonString = rs.getString(columnName);
        return parseJson(jsonString, columnName);
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String jsonString = rs.getString(columnIndex);
        return parseJson(jsonString, null);
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String jsonString = cs.getString(columnIndex);
        return parseJson(jsonString, null);
    }

    private T parseJson(String jsonString, String columnName) throws SQLException {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            Class<?> targetType = type;

            // 如果构造时没有指定具体类型，尝试从调用栈中获取javaType信息
            if (targetType == Object.class) {
                targetType = inferTypeFromStackTrace(columnName);
            }

            if (log.isDebugEnabled()) {
                log.debug("解析JSONB: column={}, targetType={}, json={}", columnName, targetType.getSimpleName(), jsonString);
            }

            @SuppressWarnings("unchecked")
            T result = (T) objectMapper.readValue(jsonString, targetType);
            return result;
        } catch (JsonProcessingException e) {
            log.error("JSON 反序列化失败, type={}, json={}", type, jsonString, e);
            throw new SQLException("JSON 反序列化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据列名推断目标类型
     * 使用缓存提高性能，基于约定优于配置的原则
     */
    private Class<?> inferTypeFromStackTrace(String columnName) {
        if (columnName == null) {
            return Object.class;
        }

        // 从缓存中获取类型映射
        Class<?> cachedType = COLUMN_TYPE_CACHE.get(columnName);
        if (cachedType != null && cachedType != Object.class) {
            if (log.isDebugEnabled()) {
                log.debug("从缓存获取类型映射: {} -> {}", columnName, cachedType.getSimpleName());
            }
            return cachedType;
        }

        log.debug("未知的JSONB列名: {}, 使用Object类型", columnName);
        return Object.class;
    }

    /**
     * 注册新的列名到类型映射
     * 允许动态扩展类型映射
     */
    public static void registerColumnTypeMapping(String columnName, Class<?> targetType) {
        COLUMN_TYPE_CACHE.put(columnName, targetType);
        log.info("注册JSONB列类型映射: {} -> {}", columnName, targetType.getName());
    }
}

