package com.xhcai.common.datasource.handler;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.databind.PropertyNamingStrategies;
import org.apache.ibatis.type.BaseTypeHandler;
import org.apache.ibatis.type.JdbcType;
import org.apache.ibatis.type.MappedJdbcTypes;
import org.apache.ibatis.type.MappedTypes;
import org.postgresql.util.PGobject;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.sql.CallableStatement;
import java.sql.PreparedStatement;
import java.sql.ResultSet;
import java.sql.SQLException;

/**
 * 通用的 PostgreSQL JSONB 类型处理器，基于 Jackson 实现。
 *
 * 使用方式：
 * 在实体字段上直接使用：
 *   @TableField(value = "xxx", typeHandler = GenericJsonbTypeHandler.class, jdbcType = JdbcType.OTHER)
 * 本处理器会通过 MyBatis 传入的 javaType 构造函数自动感知目标类型，无需为每个实体单独写 Handler。
 */
@MappedTypes(Object.class) // 让注册更通用，由构造函数的 javaType 实际确定目标类型
@MappedJdbcTypes({JdbcType.OTHER})
public class GenericJsonbTypeHandler<T> extends BaseTypeHandler<T> {

    private static final Logger log = LoggerFactory.getLogger(GenericJsonbTypeHandler.class);
    private static final ObjectMapper objectMapper = new ObjectMapper()
            .configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false)
            .setPropertyNamingStrategy(PropertyNamingStrategies.SNAKE_CASE);

    private final Class<T> type;

    /**
     * 该构造函数会被 MyBatis 在有 javaType 信息时调用（例如根据字段类型）。
     */
    @SuppressWarnings("unchecked")
    public GenericJsonbTypeHandler(Class<?> type) {
        this.type = (Class<T>) type;
    }

    /**
     * 无参构造函数：MyBatis 在 @Result 注解中使用时会调用此构造函数。
     * 此时无法获取目标类型，将在运行时动态解析。
     */
    @SuppressWarnings("unchecked")
    public GenericJsonbTypeHandler() {
        this.type = (Class<T>) Object.class; // 占位符，实际类型在运行时解析
    }

    @Override
    public void setNonNullParameter(PreparedStatement ps, int i, T parameter, JdbcType jdbcType) throws SQLException {
        try {
            PGobject jsonObject = new PGobject();
            // 使用 jsonb，保持与列定义一致
            jsonObject.setType("jsonb");

            if (parameter == null) {
                jsonObject.setValue(null);
            } else {
                String jsonString = objectMapper.writeValueAsString(parameter);
                jsonObject.setValue(jsonString);
                if (log.isDebugEnabled()) {
                    log.debug("设置JSONB参数({}): {}", type.getSimpleName(), jsonString);
                }
            }

            ps.setObject(i, jsonObject);
        } catch (JsonProcessingException e) {
            log.error("JSON 序列化失败, type={}", type, e);
            throw new SQLException("JSON 序列化失败: " + e.getMessage(), e);
        }
    }

    @Override
    public T getNullableResult(ResultSet rs, String columnName) throws SQLException {
        String jsonString = rs.getString(columnName);
        return parseJson(jsonString);
    }

    @Override
    public T getNullableResult(ResultSet rs, int columnIndex) throws SQLException {
        String jsonString = rs.getString(columnIndex);
        return parseJson(jsonString);
    }

    @Override
    public T getNullableResult(CallableStatement cs, int columnIndex) throws SQLException {
        String jsonString = cs.getString(columnIndex);
        return parseJson(jsonString);
    }

    private T parseJson(String jsonString) throws SQLException {
        if (jsonString == null || jsonString.trim().isEmpty()) {
            return null;
        }
        try {
            Class<?> targetType = type;

            // 如果构造时没有指定具体类型，尝试智能推断
            if (targetType == Object.class) {
                targetType = inferTypeFromJsonContent(jsonString);
            }

            @SuppressWarnings("unchecked")
            T result = (T) objectMapper.readValue(jsonString, targetType);
            return result;
        } catch (JsonProcessingException e) {
            log.error("JSON 反序列化失败, type={}, json={}", type, jsonString, e);
            throw new SQLException("JSON 反序列化失败: " + e.getMessage(), e);
        }
    }

    /**
     * 根据JSON内容智能推断目标类型
     * 通过分析JSON的字段结构来判断应该反序列化为哪个具体类型
     */
    private Class<?> inferTypeFromJsonContent(String jsonString) {
        try {
            JsonNode jsonNode = objectMapper.readTree(jsonString);

            // 检查是否包含SegmentConfig的特征字段
            if (jsonNode.has("type") &&
                (jsonNode.has("directory") || jsonNode.has("natural") ||
                 jsonNode.has("delimiter") || jsonNode.has("constantLength") || jsonNode.has("none"))) {

                // 尝试加载SegmentConfig类
                try {
                    return Class.forName("com.xhcai.modules.rag.entity.inner.SegmentConfig");
                } catch (ClassNotFoundException e) {
                    log.debug("SegmentConfig类未找到，使用默认类型");
                }
            }

            // 检查是否包含CleaningConfig的特征字段
            if (jsonNode.has("removeEmptyLines") || jsonNode.has("removeExtraSpaces") ||
                jsonNode.has("removeSpecialChars") || jsonNode.has("normalizeText")) {

                // 尝试加载CleaningConfig类
                try {
                    return Class.forName("com.xhcai.modules.rag.entity.inner.CleaningConfig");
                } catch (ClassNotFoundException e) {
                    log.debug("CleaningConfig类未找到，使用默认类型");
                }
            }

            // 检查是否包含RetrievalSettings的特征字段
            if (jsonNode.has("retrievalMode") || jsonNode.has("enableRerank") ||
                jsonNode.has("topK") || jsonNode.has("scoreThreshold")) {

                // 尝试加载RetrievalSettings类
                try {
                    return Class.forName("com.xhcai.modules.rag.entity.KnowledgeVectorizationConfig$RetrievalSettings");
                } catch (ClassNotFoundException e) {
                    log.debug("RetrievalSettings类未找到，使用默认类型");
                }
            }

        } catch (JsonProcessingException e) {
            log.debug("解析JSON内容失败，使用默认类型: {}", e.getMessage());
        }

        // 如果无法推断，返回Object.class，让Jackson自动处理
        return Object.class;
    }
}

