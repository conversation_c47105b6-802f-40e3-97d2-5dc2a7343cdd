<template>
  <div class="file-list-manager">


    <!-- 文件列表 -->
    <div class="file-list" v-if="!loading">
      <div class="file-list-header">
        <div class="header-cell checkbox">
          <input
            type="checkbox"
            :checked="isAllSelected"
            @change="toggleSelectAll"
            class="select-checkbox"
          >
        </div>
        <div class="header-cell file-name">文件名</div>
        <div class="header-cell file-type">类型</div>
        <div class="header-cell file-size">大小</div>
        <div class="header-cell segment-count">分段数</div>
        <div class="header-cell document-status">处理状态</div>
        <div class="header-cell char-count">字符数</div>
        <div class="header-cell permission">权限</div>
        <div class="header-cell upload-time">处理时间</div>
        <div class="header-cell actions">操作</div>
      </div>

      <div class="file-list-body">
        <div
          v-for="file in currentPageFiles"
          :key="file.id"
          class="file-row"
          :class="{ selected: selectedFileId === file.id, 'row-selected': selectedFileIds.includes(file.id) }"
        >
          <div class="cell checkbox">
            <input
              type="checkbox"
              :checked="selectedFileIds.includes(file.id)"
              @change="toggleFileSelection(file)"
              @click.stop
              class="select-checkbox"
            >
          </div>
          <div class="cell file-name">
            <div class="file-info">
              <div class="file-icon" :style="{background: file.docStyle}" :class="getFileTypeClass(file.docType)">
                {{file.docIcon}}
              </div>
              <div class="file-details">
                <div class="file-title">{{ file.name }}</div>
                <div class="file-path">{{ file.path || '/' }}</div>
              </div>
            </div>
          </div>
          <div class="cell file-type">
            <span class="type-badge" :class="getFileTypeClass(file.docType)">
              {{ file.docType?.toUpperCase() }}
            </span>
          </div>
          <div class="cell file-size">{{ formatFileSize(file.docMetadata?.file_size) }}</div>
          <div class="cell segment-count">
            <span class="count-badge">{{ file.segmentCount || 0 }}</span>
          </div>
          <div class="cell document-status">
            <span class="status-badge" :style="{background: file.documentStatusBgColor}">
              {{file.documentStatusDesc}}
            </span>
          </div>
          <div class="cell char-count">{{ formatNumber(file.wordCount || 0) }}</div>
          <div class="cell permission">
            <button
              class="permission-btn"
              @click.stop="openPermissionPanel(file)"
              :title="getPermissionText(file.permission)"
            >
              <i class="fas fa-shield-alt"></i>
              <span class="permission-text">{{ getPermissionText(file.permission) }}</span>
            </button>
          </div>
          <div class="cell upload-time">{{ formatDate(file.updateTime) }}</div>
          <div class="cell actions">
            <button
              class="action-btn"
              @click.stop="viewFileContent(file)"
              title="查看文件内容"
            >
              <i class="fas fa-eye"></i>
            </button>
            <button
              class="action-btn"
              @click.stop="viewSegments(file)"
              title="查看分段"
            >
              <i class="fas fa-list"></i>
            </button>
            <button
              class="action-btn"
              @click.stop="downloadFile(file)"
              title="下载文件"
            >
              <i class="fas fa-download"></i>
            </button>
            <button
              class="action-btn danger"
              @click.stop="deleteFile(file)"
              title="删除文件"
            >
              <i class="fas fa-trash"></i>
            </button>
          </div>
        </div>

        <!-- 空状态 -->
        <div v-if="currentPageFiles.length === 0 && !loading" class="empty-state">
          <!-- 数据源选择区域 -->
          <div class="datasource-selection">
            <div class="empty-header">
              <div class="empty-icon">📄</div>
              <h3 class="empty-title">暂无文件</h3>
              <p class="empty-description">您可以选择数据源来上传或载入数据</p>
            </div>

            <!-- 数据源选择按钮 -->
            <div class="datasource-actions">
              <div class="datasource-options">
                <div class="option-item cursor-pointer" @click="toFileUpload">
                  <i class="fas fa-upload"></i>
                  <span>本地文件上传</span>
                </div>
                <div class="option-item cursor-pointer" @click="handleSelectDataSource">
                  <i class="fas fa-database"></i>
                  <span>选择其它数据源</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <!-- 分页 -->
    <!-- 分页 -->
    <Pagination
      v-if="!loading"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :total="filteredFiles.length"
      :show-page-size-selector="true"
      :show-page-numbers="true"
      :show-jumper="true"
      @change="handlePaginationChange"
    />
    <!-- 加载状态 -->
    <div v-if="loading" class="loading-state">
      <div class="loading-spinner"></div>
      <p>正在加载文件列表...</p>
    </div>


  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  getFileTypeClass,
  formatFileSize,
  formatNumber,
  formatDate,
  getPermissionText
} from '@/utils/fileUtils'
import Pagination from '@/components/common/Pagination.vue'
import KnowledgeAPI from '@/api/knowledge'
import { v4 as uuidv4 } from 'uuid'

// Props
interface Props {
  datasetId: string
  searchQuery?: string
  fileTypeFilter?: string
  segmentStatusFilter?: string
  categoryFilter?: string | null
}

const props = defineProps<Props>()

// Emits
const emit = defineEmits<{
  fileSelected: [file: any]
  filesSelected: [files: any[]]
  refreshStats: []
  statsUpdated: [stats: { totalCount: number, totalCharCount: number, totalSegmentCount: number }]
  permissionPanelOpen: [file: any]
  fileContentView: [file: any]
  dataSourceSelect: []
}>()

// 路由和导航
const route = useRoute()
const router = useRouter()

// 响应式数据
const files = ref<any[]>([])
const loading = ref(false)
const selectedFileId = ref<string | null>(null)
const selectedFileIds = ref<string[]>([])
const currentPage = ref(1)
const pageSize = ref(20)
const totalCount = ref(0)
const datasetName = ref('')

// 计算属性
const filteredFiles = computed(() => {
  let result = files.value

  // 搜索过滤
  if (props.searchQuery?.trim()) {
    const query = props.searchQuery.toLowerCase()
    result = result.filter(file =>
      file.name.toLowerCase().includes(query) ||
      file.docType.toLowerCase().includes(query)
    )
  }

  // 类型过滤
  if (props.fileTypeFilter) {
    result = result.filter(file => file.docType === props.fileTypeFilter)
  }

  // 分段状态过滤
  if (props.segmentStatusFilter) {
    result = result.filter(file => file.segmentStatus === props.segmentStatusFilter)
  }

  // 分类过滤
  if (props.categoryFilter) {
    result = result.filter(file => file.categoryId === props.categoryFilter)
  } else if (props.categoryFilter === null) {
    // 如果明确选择了"全部文件"，则不进行分类过滤
    // 这里不需要额外处理，保持原有结果
  }

  return result
})

const isAllSelected = computed(() => {
  return currentPageFiles.value.length > 0 &&
         currentPageFiles.value.every(file => selectedFileIds.value.includes(file.id))
})

const currentPageFiles = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filteredFiles.value.slice(start, end)
})

const totalCharCount = computed(() => {
  return files.value.reduce((total, file) => total + (file.wordCount || 0), 0)
})

const totalSegmentCount = computed(() => {
  return files.value.reduce((total, file) => total + (file.segmentCount || 0), 0)
})

// 方法
const loadFiles = async () => {
  loading.value = true
  try {
    // 调用真实API获取文档列表
    const response = await KnowledgeAPI.getDocuments(props.datasetId, {
      page: currentPage.value,
      pageSize: pageSize.value,
      keyword: props.searchQuery,
      type: props.fileTypeFilter,
      status: props.segmentStatusFilter,
      categoryId: props.categoryFilter || undefined
    })

    if (response.success && response.data && response.data.records) {
      files.value = response.data.records
      totalCount.value = response.data.total

      // 发送统计信息更新事件
      emit('statsUpdated', {
        totalCount: files.value.length,
        totalCharCount: totalCharCount.value,
        totalSegmentCount: totalSegmentCount.value
      })
    }
  } catch (error) {
    console.error('加载文件列表失败:', error)
  } finally {
    loading.value = false
  }
}



const selectFile = (file: any) => {
  selectedFileId.value = file.id
  emit('fileSelected', file)
}

const viewFileContent = (file: any) => {

}

const viewSegments = (file: any) => {
  selectFile(file)
}



const downloadFile = async (file: any) => {
  try {
    const response = await KnowledgeAPI.downloadDocument(file.id)
    if (response.success) {
      // 这里应该处理文件下载，比如打开下载链接
      window.open(response.data, '_blank')
    } else {
      alert('下载失败：' + response.message)
    }
  } catch (error) {
    console.error('下载文件失败:', error)
    alert('下载文件失败')
  }
}

const deleteFile = async (file: any) => {
  if (confirm(`确定要删除文件"${file.name}"吗？`)) {
    try {
      const response = await KnowledgeAPI.deleteDocument(file.id)
      if (response.success) {
        // 重新加载文件列表
        await loadFiles()
        emit('refreshStats')
      } else {
        alert('删除失败：' + response.message)
      }
    } catch (error) {
      console.error('删除文件失败:', error)
      alert('删除文件失败')
    }
  }
}

const handlePaginationChange = (page: number, size: number) => {
  currentPage.value = page
  pageSize.value = size
  // 可以在这里添加重新加载数据的逻辑
}

// 其他方法

const openPermissionPanel = (file: any) => {
  emit('permissionPanelOpen', file)
}

// 选择相关方法
const toggleSelectAll = () => {
  if (isAllSelected.value) {
    // 取消选择当前页所有文件
    selectedFileIds.value = selectedFileIds.value.filter(id =>
      !currentPageFiles.value.some(file => file.id === id)
    )
  } else {
    // 选择当前页所有文件
    const currentPageIds = currentPageFiles.value.map(file => file.id)
    selectedFileIds.value = [...new Set([...selectedFileIds.value, ...currentPageIds])]
  }

  // 发送选中的文件列表
  const selectedFiles = files.value.filter(file => selectedFileIds.value.includes(file.id))
  emit('filesSelected', selectedFiles)
}

const toggleFileSelection = (file: any) => {
  const index = selectedFileIds.value.indexOf(file.id)
  if (index > -1) {
    selectedFileIds.value.splice(index, 1)
  } else {
    selectedFileIds.value.push(file.id)
  }

  // 发送选中的文件列表
  const selectedFiles = files.value.filter(file => selectedFileIds.value.includes(file.id))
  emit('filesSelected', selectedFiles)
}

// 监听知识库ID变化
watch(() => props.datasetId, () => {
  loadFiles()
})

// 监听搜索和过滤参数变化，重置分页
watch(() => [props.searchQuery, props.fileTypeFilter, props.segmentStatusFilter, props.categoryFilter], () => {
  currentPage.value = 1
  selectedFileIds.value = [] // 清空选择
})

// 文件上传路由
const toFileUpload = () => {
  router.push({
    name: 'LocalFileFlow',
    query: {
      datasetId: props.datasetId,
      name: datasetName.value,
      batchId: uuidv4()
    }
  })
}

// 数据源选择处理路由
const handleSelectDataSource = () => {
  // 触发自定义事件，让父组件处理路由跳转
  router.push({ path: `/knowledge/${props.datasetId}/datasource`, query: { name: datasetName.value} });
}

// 暴露方法给父组件
const refreshFiles = () => {
  loadFiles()
}

defineExpose({
  refreshFiles
})

// 生命周期
onMounted(() => {
  datasetName.value = route.query.name as string
  loadFiles()
})
</script>

<style scoped>

.file-type-pdf { background: #fef3c7; }
.file-type-doc { background: #dbeafe; }
.file-type-docx { background: #dbeafe; }
.file-type-txt { background: #f3f4f6; }
.file-type-md { background: #ecfdf5; }
.file-type-xlsx { background: #fef2f2; }

.file-list-manager {
  height: 100%;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex容器可以正确收缩 */
  width: 100%;
  overflow: hidden;
}

.file-list {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子项可以收缩 */
}

.file-list-header {
  display: grid !important;
  grid-template-columns: 40px 1fr 80px 90px 80px 90px 110px 130px 160px !important;
  gap: 12px;
  padding: 12px 20px;
  background: #f8fafc;
  border-bottom: 1px solid #e2e8f0;
  font-size: 12px;
  font-weight: 600;
  color: #64748b;
  text-transform: uppercase;
  min-width: 1220px; /* 设置足够的最小宽度 */
  grid-auto-flow: column; /* 强制列布局 */
}

.file-list-body {
  flex: 1;
  overflow: auto; /* 允许水平和垂直滚动 */
  min-height: 0; /* 确保flex子项可以收缩 */
  max-height: calc(100vh - 240px); /* 增加预留空间，为分页组件留出更多空间 */
  width: 100%;
  min-width: 1220px; /* 确保有足够宽度 */
}

.file-row {
  display: grid !important;
  grid-template-columns: 40px 1fr 80px 90px 80px 90px 110px 130px 160px !important;
  gap: 12px;
  padding: 16px 20px;
  border-bottom: 1px solid #f1f5f9;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 1220px; /* 设置足够的最小宽度 */
  grid-auto-flow: column; /* 强制列布局 */
}

.file-row:hover {
  background: #e9f4ff;
}

.file-row.selected {
  background: #eff6ff;
  border-color: #3b82f6;
}

.file-row.row-selected {
  background: #f0f9ff;
  border-left: 3px solid #3b82f6;
}

.cell {
  display: flex !important;
  align-items: center;
  font-size: 14px;
  min-width: 0;
  overflow: hidden;
  white-space: nowrap;
  flex-shrink: 0;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
}

.file-icon {
  font-size: 20px;
  flex-shrink: 0;
}

.file-details {
  min-width: 0;
}

.file-title {
  font-weight: 500;
  color: #1e293b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-path {
  font-size: 12px;
  color: #64748b;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.type-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

.count-badge {
  background: #f1f5f9;
  color: #475569;
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 500;
}

.select-checkbox {
  width: 16px;
  height: 16px;
  cursor: pointer;
}

.status-badge {
  padding: 2px 8px;
  border-radius: 4px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
}

/* 为特定列设置最小宽度 */
.cell.file-type {
  min-width: 60px;
}

.cell.file-size {
  min-width: 70px;
}

.cell.segment-count {
  min-width: 60px;
}

.cell.document-status {
  min-width: 90px;
}

.cell.char-count {
  min-width: 80px;
}

.cell.permission {
  min-width: 90px;
}

.cell.upload-time {
  min-width: 120px;
}

.actions {
  display: flex;
  align-items: center;
  gap: 6px;
  justify-content: flex-start;
  min-width: 160px; /* 确保操作按钮有足够空间 */
  flex-wrap: nowrap;
  width: 100%;
}

.action-btn {
  width: 24px !important;
  height: 24px !important;
  border: none;
  border-radius: 4px;
  background: #f8fafc;
  color: #64748b;
  cursor: pointer;
  font-size: 11px;
  display: flex !important;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  flex-shrink: 0 !important;
  min-width: 24px;
}

.action-btn:hover {
  background: #e2e8f0;
  color: #334155;
}

.action-btn.danger:hover {
  background: #fef2f2;
  color: #dc2626;
}

.permission-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  background: white;
  color: #64748b;
  cursor: pointer;
  font-size: 12px;
  transition: all 0.2s ease;
  max-width: 100%;
}

.permission-btn:hover {
  background: #f8fafc;
  border-color: #3b82f6;
  color: #3b82f6;
}

.permission-text {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  max-width: 80px;
}

.empty-state {
  padding: 40px 20px;
  color: #64748b;
  min-height: 500px;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.datasource-selection {
  text-align: center;
  max-width: 500px;
  width: 100%;
}

.empty-header {
  margin-bottom: 32px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  font-size: 20px;
  font-weight: 600;
  color: #374151;
  margin-bottom: 8px;
}

.empty-description {
  font-size: 14px;
  color: #6b7280;
  margin-bottom: 0;
}

.datasource-actions {
  margin-top: 24px;
}

.datasource-btn i {
  font-size: 16px;
}

.datasource-options {
  margin-top: 24px;
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
  gap: 16px;
  max-width: 400px;
  margin-left: auto;
  margin-right: auto;
}

.option-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  background: #f8fafc;
  border: 1px solid #e2e8f0;
  border-radius: 8px;
  font-size: 12px;
  color: #64748b;
  transition: all 0.2s ease;
}

.option-item:hover {
  background: #f1f5f9;
  border-color: #cbd5e1;
  color: #475569;
}

.option-item i {
  font-size: 20px;
  color: #94a3b8;
}

.option-item:hover i {
  color: #64748b;
}

.datasource-selection {
  width: 100%;
  max-width: 1200px;
  margin: 0 auto;
}

.empty-header {
  text-align: center;
  margin-bottom: 40px;
}

.empty-icon {
  font-size: 48px;
  margin-bottom: 16px;
  opacity: 0.5;
}

.empty-title {
  font-size: 24px;
  font-weight: 600;
  color: #1e293b;
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 16px;
  color: #64748b;
  margin: 0;
}

.action-btn {
  width: 32px;
  height: 32px;
  border: none;
  border-radius: 6px;
  background: #f1f5f9;
  color: #64748b;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.action-btn:hover {
  background: #e2e8f0;
  color: #475569;
}

.feature-item i {
  font-size: 12px;
  color: #3b82f6;
}

.stat-item i {
  font-size: 12px;
}

.status-badge {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.status-badge.active {
  background: #dcfce7;
  color: #166534;
}

.status-badge.inactive {
  background: #f1f5f9;
  color: #64748b;
}

.status-badge.error {
  background: #fef2f2;
  color: #dc2626;
}

.loading-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: #64748b;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid #e2e8f0;
  border-top: 3px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}
</style>
