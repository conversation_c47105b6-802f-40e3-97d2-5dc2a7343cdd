package com.xhcai.modules.system.entity;

import java.time.LocalDateTime;

import com.baomidou.mybatisplus.annotation.TableField;
import com.baomidou.mybatisplus.annotation.TableName;
import com.fasterxml.jackson.annotation.JsonFormat;
import com.xhcai.common.datasource.entity.BaseWithTenantIDEntity;

import io.swagger.v3.oas.annotations.media.Schema;
import jakarta.persistence.Column;
import jakarta.persistence.Entity;
import jakarta.persistence.Index;
import jakarta.persistence.Table;
import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import jakarta.validation.constraints.Pattern;
import jakarta.validation.constraints.Size;
import lombok.Data;

/**
 * API密钥管理实体
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Data
@Entity
@Table(name = "sys_api_key", indexes = {
    @Index(name = "idx_api_key_hash", columnList = "key_hash"),
    @Index(name = "idx_api_target", columnList = "target_type, target_id"),
    @Index(name = "idx_api_status", columnList = "status"),
    @Index(name = "idx_api_tenant_id", columnList = "tenant_id"),
    @Index(name = "idx_api_create_by", columnList = "create_by"),
    @Index(name = "idx_api_expires_at", columnList = "expires_at"),
    @Index(name = "idx_api_create_time", columnList = "create_time")
})
@Schema(description = "API密钥管理")
@TableName("sys_api_key")
public class SysApiKey extends BaseWithTenantIDEntity {

    private static final long serialVersionUID = 1L;

    /**
     * 密钥名称
     */
    @Column(name = "key_name", length = 100)
    @Schema(description = "密钥名称", example = "智能体API密钥")
    @NotBlank(message = "密钥名称不能为空")
    @Size(max = 100, message = "密钥名称长度不能超过100个字符")
    @TableField("key_name")
    private String keyName;

    /**
     * 授权密钥Key（JWT格式）
     */
    @Column(name = "key_value", columnDefinition = "TEXT")
    @Schema(description = "授权密钥Key（JWT格式）")
    @TableField("key_value")
    private String keyValue;

    /**
     * 密钥哈希值（用于快速查找）
     */
    @Column(name = "key_hash", length = 64)
    @Schema(description = "密钥哈希值")
    @Size(max = 64, message = "密钥哈希值长度不能超过64个字符")
    @TableField("key_hash")
    private String keyHash;

    /**
     * 申请单位
     */
    @Column(name = "applicant_unit", length = 100)
    @Schema(description = "申请单位", example = "北京科技有限公司")
    @NotBlank(message = "申请单位不能为空")
    @Size(max = 100, message = "申请单位长度不能超过100个字符")
    @TableField("applicant_unit")
    private String applicantUnit;

    /**
     * 申请人姓名
     */
    @Column(name = "applicant_name", length = 50)
    @Schema(description = "申请人姓名", example = "张三")
    @NotBlank(message = "申请人姓名不能为空")
    @Size(max = 50, message = "申请人姓名长度不能超过50个字符")
    @TableField("applicant_name")
    private String applicantName;

    /**
     * 申请人手机号
     */
    @Column(name = "applicant_phone", length = 20)
    @Schema(description = "申请人手机号", example = "13800138001")
    @NotBlank(message = "申请人手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "申请人手机号格式不正确")
    @TableField("applicant_phone")
    private String applicantPhone;

    /**
     * 责任单位
     */
    @Column(name = "responsible_unit", length = 100)
    @Schema(description = "责任单位", example = "技术部")
    @NotBlank(message = "责任单位不能为空")
    @Size(max = 100, message = "责任单位长度不能超过100个字符")
    @TableField("responsible_unit")
    private String responsibleUnit;

    /**
     * 责任人姓名
     */
    @Column(name = "responsible_name", length = 50)
    @Schema(description = "责任人姓名", example = "李四")
    @NotBlank(message = "责任人姓名不能为空")
    @Size(max = 50, message = "责任人姓名长度不能超过50个字符")
    @TableField("responsible_name")
    private String responsibleName;

    /**
     * 责任人手机号
     */
    @Column(name = "responsible_phone", length = 20)
    @Schema(description = "责任人手机号", example = "13800138002")
    @NotBlank(message = "责任人手机号不能为空")
    @Pattern(regexp = "^1[3-9]\\d{9}$", message = "责任人手机号格式不正确")
    @TableField("responsible_phone")
    private String responsiblePhone;

    /**
     * 访问对象类型
     */
    @Column(name = "target_type", length = 20)
    @Schema(description = "访问对象类型", example = "agent", allowableValues = {"agent", "knowledge", "graph", "model"})
    @NotBlank(message = "访问对象类型不能为空")
    @Pattern(regexp = "^(agent|knowledge|graph|model)$", message = "访问对象类型值必须为agent、knowledge、graph或model")
    @TableField("target_type")
    private String targetType;

    /**
     * 访问对象ID
     */
    @Column(name = "target_id", length = 36)
    @Schema(description = "访问对象ID", example = "123e4567-e89b-12d3-a456-426614174000")
    @NotBlank(message = "访问对象ID不能为空")
    @Size(max = 36, message = "访问对象ID长度不能超过36个字符")
    @TableField("target_id")
    private String targetId;

    /**
     * 访问对象名称
     */
    @Column(name = "target_name", length = 100)
    @Schema(description = "访问对象名称", example = "GPT-4智能助手")
    @NotBlank(message = "访问对象名称不能为空")
    @Size(max = 100, message = "访问对象名称长度不能超过100个字符")
    @TableField("target_name")
    private String targetName;

    /**
     * 访问频率
     */
    @Column(name = "access_frequency", length = 20)
    @Schema(description = "访问频率", example = "medium", allowableValues = {"low", "medium", "high"})
    @NotBlank(message = "访问频率不能为空")
    @Pattern(regexp = "^(low|medium|high)$", message = "访问频率值必须为low、medium或high")
    @TableField("access_frequency")
    private String accessFrequency;

    /**
     * 访问次数限制（0表示无限制）
     */
    @Column(name = "access_count_limit")
    @Schema(description = "访问次数限制", example = "1000")
    @TableField("access_count_limit")
    private Integer accessCountLimit;

    /**
     * 已使用访问次数
     */
    @Column(name = "access_count_used")
    @Schema(description = "已使用访问次数", example = "100")
    @TableField("access_count_used")
    private Integer accessCountUsed;

    /**
     * 状态
     */
    @Column(name = "status", length = 20)
    @Schema(description = "状态", example = "pending", allowableValues = {"pending", "approved", "rejected", "disabled"})
    @Pattern(regexp = "^(pending|approved|rejected|disabled)$", message = "状态值必须为pending、approved、rejected或disabled")
    @TableField("status")
    private String status;

    /**
     * 审批人ID
     */
    @Column(name = "approve_by", length = 36)
    @Schema(description = "审批人ID")
    @TableField("approve_by")
    private String approveBy;

    /**
     * 审批时间
     */
    @Column(name = "approve_time")
    @Schema(description = "审批时间")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("approve_time")
    private LocalDateTime approveTime;

    /**
     * 拒绝原因
     */
    @Column(name = "reject_reason", length = 500)
    @Schema(description = "拒绝原因")
    @Size(max = 500, message = "拒绝原因长度不能超过500个字符")
    @TableField("reject_reason")
    private String rejectReason;

    /**
     * 密钥过期时间
     */
    @Column(name = "expires_at")
    @Schema(description = "密钥过期时间")
    @NotNull(message = "密钥过期时间不能为空")
    @JsonFormat(pattern = "yyyy-MM-dd HH:mm:ss")
    @TableField("expires_at")
    private LocalDateTime expiresAt;
}