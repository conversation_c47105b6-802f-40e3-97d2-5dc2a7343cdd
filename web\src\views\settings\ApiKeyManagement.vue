<template>
  <div class="unified-api-key-management">
    <!-- 页面标题 -->
    <div class="config-header">
      <div class="header-content">
        <div class="header-left">
          <h2 class="header-title">
            <el-icon class="text-blue-500 text-3xl"><Key /></el-icon>
            API密钥管理
          </h2>
          <p class="header-subtitle">统一管理智能体、知识库、知识图谱和模型的API密钥申请与审批</p>
        </div>
      </div>
    </div>

    <!-- 统计卡片 -->
    <div class="stats-grid grid grid-cols-1 md:grid-cols-5 gap-6 my-8">
      <div class="stat-card bg-gradient-to-r from-blue-50 to-blue-100 rounded-xl p-6 border border-blue-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-blue-600 text-sm font-medium">总申请数</p>
            <p class="text-2xl font-bold text-blue-800">{{ stats.total }}</p>
          </div>
          <el-icon class="text-blue-500 text-3xl"><Document /></el-icon>
        </div>
      </div>
      
      <div class="stat-card bg-gradient-to-r from-yellow-50 to-yellow-100 rounded-xl p-6 border border-yellow-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-yellow-600 text-sm font-medium">待审批</p>
            <p class="text-2xl font-bold text-yellow-800">{{ stats.pending }}</p>
          </div>
          <el-icon class="text-yellow-500 text-3xl"><Clock /></el-icon>
        </div>
      </div>
      
      <div class="stat-card bg-gradient-to-r from-green-50 to-green-100 rounded-xl p-6 border border-green-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-green-600 text-sm font-medium">已通过</p>
            <p class="text-2xl font-bold text-green-800">{{ stats.approved }}</p>
          </div>
          <el-icon class="text-green-500 text-3xl"><CircleCheck /></el-icon>
        </div>
      </div>
      
      <div class="stat-card bg-gradient-to-r from-red-50 to-red-100 rounded-xl p-6 border border-red-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-red-600 text-sm font-medium">已拒绝</p>
            <p class="text-2xl font-bold text-red-800">{{ stats.rejected }}</p>
          </div>
          <el-icon class="text-red-500 text-3xl"><CircleClose /></el-icon>
        </div>
      </div>
      
      <div class="stat-card bg-gradient-to-r from-purple-50 to-purple-100 rounded-xl p-6 border border-purple-200">
        <div class="flex items-center justify-between">
          <div>
            <p class="text-purple-600 text-sm font-medium">已禁用</p>
            <p class="text-2xl font-bold text-purple-800">{{ stats.disabled }}</p>
          </div>
          <el-icon class="text-purple-500 text-3xl"><Lock /></el-icon>
        </div>
      </div>
    </div>

    <!-- 筛选和搜索 -->
    <div class="filter-section bg-white rounded-xl shadow-sm border border-gray-200 p-6 mb-6">
      <div class="flex flex-col lg:flex-row gap-4 items-center justify-between">
        <div class="flex flex-col md:flex-row gap-4 flex-1">
          <el-select v-model="filters.status" placeholder="选择状态" clearable class="w-full md:w-48">
            <el-option label="全部状态" value="" />
            <el-option label="待审批" value="pending" />
            <el-option label="已通过" value="approved" />
            <el-option label="已拒绝" value="rejected" />
            <el-option label="已禁用" value="disabled" />
          </el-select>
          
          <el-select v-model="filters.targetType" placeholder="选择类型" clearable class="w-full md:w-48">
            <el-option label="全部类型" value="" />
            <el-option label="智能体" value="agent" />
            <el-option label="知识库" value="knowledge" />
            <el-option label="知识图谱" value="graph" />
            <el-option label="模型" value="model" />
          </el-select>
          
          <el-input
            v-model="filters.keyword"
            placeholder="搜索密钥名称、申请人或目标名称"
            clearable
            class="w-full md:w-64"
          >
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        
        <div class="flex gap-2">
          <el-button type="primary" @click="loadApiKeys" :loading="loading">
            <el-icon><Refresh /></el-icon>
            刷新
          </el-button>
          
          <el-button 
            type="success" 
            @click="batchApprove" 
            :disabled="selectedKeys.length === 0"
            v-if="hasSelectedPendingKeys"
          >
            <el-icon><CircleCheck /></el-icon>
            批量通过
          </el-button>
          
          <el-button 
            type="danger" 
            @click="batchReject" 
            :disabled="selectedKeys.length === 0"
            v-if="hasSelectedPendingKeys"
          >
            <el-icon><CircleClose /></el-icon>
            批量拒绝
          </el-button>
        </div>
      </div>
    </div>

    <!-- API密钥列表 -->
    <div class="api-key-list bg-white rounded-xl shadow-sm border border-gray-200">
      <!-- 加载状态 -->
      <div v-if="loading" class="p-8 text-center">
        <el-icon class="text-4xl text-gray-400 animate-spin"><Loading /></el-icon>
        <p class="text-gray-500 mt-2">加载中...</p>
      </div>
      
      <!-- 空状态 -->
      <div v-else-if="filteredApiKeys.length === 0" class="p-8 text-center">
        <el-icon class="text-6xl text-gray-300"><Key /></el-icon>
        <p class="text-gray-500 mt-4">暂无API密钥申请记录</p>
        <p class="text-gray-400 text-sm mt-2">用户可以在智能体或知识库页面申请API密钥</p>
      </div>
      
      <!-- 密钥列表 -->
      <div v-else>
        <!-- 表头 -->
        <div class="table-header grid grid-cols-12 gap-4 p-4 bg-gray-50 border-b border-gray-200 font-medium text-gray-700 text-sm">
          <div class="col-span-1">
            <el-checkbox 
              v-model="selectAll" 
              @change="handleSelectAll"
              :indeterminate="isIndeterminate"
            />
          </div>
          <div class="col-span-2">密钥名称</div>
          <div class="col-span-1">类型</div>
          <div class="col-span-2">目标名称</div>
          <div class="col-span-2">申请人</div>
          <div class="col-span-1">状态</div>
          <div class="col-span-2">申请时间</div>
          <div class="col-span-1">操作</div>
        </div>
        
        <!-- 表格内容 -->
        <div class="divide-y divide-gray-200">
          <div
            v-for="apiKey in paginatedApiKeys"
            :key="apiKey.id"
            class="table-row grid grid-cols-12 gap-4 p-4 hover:bg-gray-50 transition-colors duration-200 text-sm"
          >
            <div class="col-span-1 flex items-center">
              <el-checkbox 
                v-model="selectedKeys" 
                @change="handleSelectionChange"
              />
            </div>
            
            <div class="col-span-2 flex items-center">
              <div>
                <p class="font-medium text-gray-800">{{ apiKey.keyName }}</p>
                <p class="text-xs text-gray-500">{{ apiKey.applicantUnit }}</p>
              </div>
            </div>
            
            <div class="col-span-1 flex items-center">
              <el-tag :type="getTargetTypeTagType(apiKey.targetType)" size="small">
                {{ getTargetTypeText(apiKey.targetType) }}
              </el-tag>
            </div>
            
            <div class="col-span-2 flex items-center">
              <div>
                <p class="font-medium text-gray-800">{{ apiKey.targetName }}</p>
                <p class="text-xs text-gray-500">ID: {{ apiKey.targetId }}</p>
              </div>
            </div>
            
            <div class="col-span-2 flex items-center">
              <div>
                <p class="font-medium text-gray-800">{{ apiKey.applicantName }}</p>
                <p class="text-xs text-gray-500">{{ apiKey.applicantPhone }}</p>
              </div>
            </div>
            
            <div class="col-span-1 flex items-center">
              <el-tag :type="getStatusTagType(apiKey.status)" size="small">
                {{ getStatusText(apiKey.status) }}
              </el-tag>
            </div>
            
            <div class="col-span-2 flex items-center">
              <div>
                <p class="text-gray-800">{{ formatDate(apiKey.createTime) }}</p>
                <p v-if="apiKey.approveTime" class="text-xs text-green-600">
                  审批: {{ formatDate(apiKey.approveTime) }}
                </p>
              </div>
            </div>
            
            <div class="col-span-1 flex items-center gap-1">
              <el-button
                v-if="apiKey.status === 'pending'"
                type="success"
                size="small"
                @click="approveKey(apiKey)"
              >
                通过
              </el-button>
              
              <el-button
                v-if="apiKey.status === 'pending'"
                type="danger"
                size="small"
                @click="rejectKey(apiKey)"
              >
                拒绝
              </el-button>
              
              <el-button
                v-if="apiKey.status === 'approved'"
                type="warning"
                size="small"
                @click="disableKey(apiKey)"
              >
                禁用
              </el-button>
              
              <el-button
                type="primary"
                size="small"
                @click="viewKeyDetails(apiKey)"
              >
                详情
              </el-button>
            </div>
          </div>
        </div>
        
        <!-- 分页 -->
        <div class="p-6 border-t border-gray-200">
          <el-pagination
            v-model:current-page="pagination.current"
            v-model:page-size="pagination.size"
            :total="filteredApiKeys.length"
            :page-sizes="[10, 20, 50, 100]"
            layout="total, sizes, prev, pager, next, jumper"
            class="justify-center"
          />
        </div>
      </div>
    </div>

    <!-- 密钥详情对话框 -->
    <el-dialog
      v-model="detailDialog.visible"
      title="API密钥详情"
      width="800px"
      :before-close="closeDetailDialog"
    >
      <div v-if="detailDialog.apiKey" class="space-y-6">
        <!-- 基本信息 -->
        <div class="bg-gray-50 rounded-lg p-4">
          <h4 class="text-lg font-semibold text-gray-800 mb-4">基本信息</h4>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium text-gray-600">密钥名称:</span>
              <span class="ml-2 text-gray-800">{{ detailDialog.apiKey.keyName }}</span>
            </div>
            <div>
              <span class="font-medium text-gray-600">目标类型:</span>
              <span class="ml-2 text-gray-800">{{ getTargetTypeText(detailDialog.apiKey.targetType) }}</span>
            </div>
            <div>
              <span class="font-medium text-gray-600">目标名称:</span>
              <span class="ml-2 text-gray-800">{{ detailDialog.apiKey.targetName }}</span>
            </div>
            <div>
              <span class="font-medium text-gray-600">访问频率:</span>
              <span class="ml-2 text-gray-800">{{ getFrequencyText(detailDialog.apiKey.accessFrequency) }}</span>
            </div>
            <div>
              <span class="font-medium text-gray-600">访问次数限制:</span>
              <span class="ml-2 text-gray-800">{{ detailDialog.apiKey.accessCountLimit || '无限制' }}</span>
            </div>
            <div>
              <span class="font-medium text-gray-600">已使用次数:</span>
              <span class="ml-2 text-gray-800">{{ detailDialog.apiKey.accessCountUsed || 0 }}</span>
            </div>
          </div>
        </div>
        
        <!-- 申请人信息 -->
        <div class="bg-blue-50 rounded-lg p-4">
          <h4 class="text-lg font-semibold text-blue-800 mb-4">申请人信息</h4>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium text-blue-600">申请单位:</span>
              <span class="ml-2 text-blue-800">{{ detailDialog.apiKey.applicantUnit }}</span>
            </div>
            <div>
              <span class="font-medium text-blue-600">申请人:</span>
              <span class="ml-2 text-blue-800">{{ detailDialog.apiKey.applicantName }}</span>
            </div>
            <div>
              <span class="font-medium text-blue-600">联系电话:</span>
              <span class="ml-2 text-blue-800">{{ detailDialog.apiKey.applicantPhone }}</span>
            </div>
            <div>
              <span class="font-medium text-blue-600">责任单位:</span>
              <span class="ml-2 text-blue-800">{{ detailDialog.apiKey.responsibleUnit }}</span>
            </div>
            <div>
              <span class="font-medium text-blue-600">责任人:</span>
              <span class="ml-2 text-blue-800">{{ detailDialog.apiKey.responsibleName }}</span>
            </div>
            <div>
              <span class="font-medium text-blue-600">责任人电话:</span>
              <span class="ml-2 text-blue-800">{{ detailDialog.apiKey.responsiblePhone }}</span>
            </div>
          </div>
        </div>
        
        <!-- 审批信息 -->
        <div v-if="detailDialog.apiKey.status !== 'pending'" class="bg-green-50 rounded-lg p-4">
          <h4 class="text-lg font-semibold text-green-800 mb-4">审批信息</h4>
          <div class="grid grid-cols-2 gap-4 text-sm">
            <div>
              <span class="font-medium text-green-600">审批状态:</span>
              <span class="ml-2 text-green-800">{{ getStatusText(detailDialog.apiKey.status) }}</span>
            </div>
            <div v-if="detailDialog.apiKey.approveBy">
              <span class="font-medium text-green-600">审批人:</span>
              <span class="ml-2 text-green-800">{{ detailDialog.apiKey.approveBy }}</span>
            </div>
            <div v-if="detailDialog.apiKey.approveTime">
              <span class="font-medium text-green-600">审批时间:</span>
              <span class="ml-2 text-green-800">{{ formatDate(detailDialog.apiKey.approveTime) }}</span>
            </div>
            <div v-if="detailDialog.apiKey.rejectReason">
              <span class="font-medium text-red-600">拒绝原因:</span>
              <span class="ml-2 text-red-800">{{ detailDialog.apiKey.rejectReason }}</span>
            </div>
          </div>
        </div>
        
        <!-- API密钥值 -->
        <div v-if="detailDialog.apiKey.status === 'approved' && detailDialog.apiKey.keyValue" class="bg-yellow-50 rounded-lg p-4">
          <div class="flex items-center justify-between mb-2">
            <h4 class="text-lg font-semibold text-yellow-800">API密钥</h4>
            <el-button
              type="primary"
              size="small"
              @click="copyApiKey"
            >
              <el-icon><CopyDocument /></el-icon>
              复制
            </el-button>
          </div>
          <div class="font-mono text-sm bg-white p-3 rounded border break-all">
            {{ detailDialog.showKey ? detailDialog.apiKey.keyValue : '••••••••••••••••••••••••••••••••' }}
          </div>
          <div class="mt-2">
            <el-button
              type="text"
              size="small"
              @click="detailDialog.showKey = !detailDialog.showKey"
            >
              <el-icon><View /></el-icon>
              {{ detailDialog.showKey ? '隐藏' : '显示' }}密钥
            </el-button>
          </div>
        </div>
      </div>
    </el-dialog>

    <!-- 拒绝原因对话框 -->
    <el-dialog
      v-model="rejectDialog.visible"
      title="拒绝申请"
      width="500px"
    >
      <div class="space-y-4">
        <p class="text-gray-700">请输入拒绝原因：</p>
        <el-input
          v-model="rejectDialog.reason"
          type="textarea"
          :rows="4"
          placeholder="请输入拒绝原因..."
          maxlength="500"
          show-word-limit
        />
      </div>
      
      <template #footer>
        <div class="flex justify-end gap-2">
          <el-button @click="rejectDialog.visible = false">取消</el-button>
          <el-button type="danger" @click="confirmReject" :loading="rejectDialog.loading">
            确认拒绝
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, onMounted, watch } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import {
  Key,
  Document,
  CircleCheck,
  Clock,
  CircleClose,
  Lock,
  Search,
  Refresh,
  Loading,
  View,
  CopyDocument
} from '@element-plus/icons-vue'
import {
  getPendingApiKeys,
  approveApiKey,
  batchApproveApiKey,
  disableApiKey,
  getApiKeyInfo,
  type ApiKeyInfo,
  type ApiKeyQueryParams
} from '@/api/system/apikey'

// 响应式数据
const loading = ref(false)
const apiKeys = ref<ApiKeyInfo[]>([])
const selectedKeys = ref<string[]>([])
const selectAll = ref(false)

// 统计数据
const stats = ref({
  total: 0,
  pending: 0,
  approved: 0,
  rejected: 0,
  disabled: 0
})

// 筛选条件
const filters = ref({
  status: '',
  targetType: '',
  keyword: ''
})

// 分页
const pagination = ref({
  current: 1,
  size: 20
})

// 详情对话框
const detailDialog = ref({
  visible: false,
  apiKey: null as ApiKeyInfo | null,
  showKey: false
})

// 拒绝对话框
const rejectDialog = ref({
  visible: false,
  reason: '',
  loading: false,
  currentKey: null as ApiKeyInfo | null
})

// 计算属性
const filteredApiKeys = computed(() => {
  let result = apiKeys.value

  // 状态筛选
  if (filters.value.status) {
    result = result.filter(key => key.status === filters.value.status)
  }

  // 类型筛选
  if (filters.value.targetType) {
    result = result.filter(key => key.targetType === filters.value.targetType)
  }

  // 关键词搜索
  if (filters.value.keyword) {
    const keyword = filters.value.keyword.toLowerCase()
    result = result.filter(key =>
      key.keyName.toLowerCase().includes(keyword) ||
      key.applicantName.toLowerCase().includes(keyword) ||
      key.targetName.toLowerCase().includes(keyword)
    )
  }

  return result
})

const paginatedApiKeys = computed(() => {
  const start = (pagination.value.current - 1) * pagination.value.size
  const end = start + pagination.value.size
  return filteredApiKeys.value.slice(start, end)
})

const hasSelectedPendingKeys = computed(() => {
  return selectedKeys.value.some(id => {
    const key = apiKeys.value.find(k => k.id === id)
    return key?.status === 'pending'
  })
})

const isIndeterminate = computed(() => {
  const selectedCount = selectedKeys.value.length
  const totalCount = paginatedApiKeys.value.length
  return selectedCount > 0 && selectedCount < totalCount
})

// 方法
const loadApiKeys = async () => {
  try {
    loading.value = true
    const response = await getPendingApiKeys({
      pageNum: 1,
      pageSize: 30 // 获取所有数据，前端分页
    })

    if (response.code === 200) {
      apiKeys.value = response.data.records
      updateStats()
    } else {
      ElMessage.error(response.message || '加载API密钥列表失败')
    }
  } catch (error) {
    console.error('加载API密钥列表失败:', error)
    ElMessage.error('加载API密钥列表失败')
  } finally {
    loading.value = false
  }
}

const updateStats = () => {
  stats.value.total = apiKeys.value.length
  stats.value.pending = apiKeys.value.filter(key => key.status === 'pending').length
  stats.value.approved = apiKeys.value.filter(key => key.status === 'approved').length
  stats.value.rejected = apiKeys.value.filter(key => key.status === 'rejected').length
  stats.value.disabled = apiKeys.value.filter(key => key.status === 'disabled').length
}

const getStatusText = (status: string) => {
  switch (status) {
    case 'pending': return '待审批'
    case 'approved': return '已通过'
    case 'rejected': return '已拒绝'
    case 'disabled': return '已禁用'
    default: return status
  }
}

const getStatusTagType = (status: string) => {
  switch (status) {
    case 'pending': return 'warning'
    case 'approved': return 'success'
    case 'rejected': return 'danger'
    case 'disabled': return 'info'
    default: return 'info'
  }
}

const getTargetTypeText = (targetType: string) => {
  switch (targetType) {
    case 'agent': return '智能体'
    case 'knowledge': return '知识库'
    case 'graph': return '知识图谱'
    case 'model': return '模型'
    default: return targetType
  }
}

const getTargetTypeTagType = (targetType: string) => {
  switch (targetType) {
    case 'agent': return 'primary'
    case 'knowledge': return 'success'
    case 'graph': return 'warning'
    case 'model': return 'danger'
    default: return 'info'
  }
}

const getFrequencyText = (frequency: string) => {
  switch (frequency) {
    case 'low': return '低频'
    case 'medium': return '中频'
    case 'high': return '高频'
    default: return frequency
  }
}

const formatDate = (dateStr: string) => {
  if (!dateStr) return '-'
  return new Date(dateStr).toLocaleString('zh-CN')
}

// 选择相关方法
const handleSelectAll = (checked: boolean) => {
  if (checked) {
    selectedKeys.value = paginatedApiKeys.value.map(key => key.id)
  } else {
    selectedKeys.value = []
  }
}

const handleSelectionChange = () => {
  const selectedCount = selectedKeys.value.length
  const totalCount = paginatedApiKeys.value.length
  selectAll.value = selectedCount === totalCount && totalCount > 0
}

// 审批相关方法
const approveKey = async (apiKey: ApiKeyInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要通过"${apiKey.keyName}"的申请吗？`,
      '确认通过',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }
    )

    const response = await approveApiKey(apiKey.id, true)
    if (response.code === 200) {
      ElMessage.success('审批通过')
      await loadApiKeys() // 重新加载列表
    } else {
      ElMessage.error(response.message || '审批失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('审批失败:', error)
      ElMessage.error('审批失败')
    }
  }
}

const rejectKey = (apiKey: ApiKeyInfo) => {
  rejectDialog.value.currentKey = apiKey
  rejectDialog.value.reason = ''
  rejectDialog.value.visible = true
}

const confirmReject = async () => {
  if (!rejectDialog.value.reason.trim()) {
    ElMessage.warning('请输入拒绝原因')
    return
  }

  try {
    rejectDialog.value.loading = true
    const response = await approveApiKey(
      rejectDialog.value.currentKey!.id,
      false,
      rejectDialog.value.reason
    )

    if (response.code === 200) {
      ElMessage.success('已拒绝申请')
      rejectDialog.value.visible = false
      await loadApiKeys() // 重新加载列表
    } else {
      ElMessage.error(response.message || '操作失败')
    }
  } catch (error) {
    console.error('拒绝申请失败:', error)
    ElMessage.error('拒绝申请失败')
  } finally {
    rejectDialog.value.loading = false
  }
}

const batchApprove = async () => {
  const pendingKeys = selectedKeys.value.filter(id => {
    const key = apiKeys.value.find(k => k.id === id)
    return key?.status === 'pending'
  })

  if (pendingKeys.length === 0) {
    ElMessage.warning('请选择待审批的密钥')
    return
  }

  try {
    await ElMessageBox.confirm(
      `确定要批量通过 ${pendingKeys.length} 个密钥申请吗？`,
      '确认批量通过',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'success'
      }
    )

    const response = await batchApproveApiKey(pendingKeys, true)
    if (response.code === 200) {
      ElMessage.success(`已批量通过 ${pendingKeys.length} 个申请`)
      selectedKeys.value = []
      await loadApiKeys() // 重新加载列表
    } else {
      ElMessage.error(response.message || '批量审批失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量审批失败:', error)
      ElMessage.error('批量审批失败')
    }
  }
}

const batchReject = async () => {
  const pendingKeys = selectedKeys.value.filter(id => {
    const key = apiKeys.value.find(k => k.id === id)
    return key?.status === 'pending'
  })

  if (pendingKeys.length === 0) {
    ElMessage.warning('请选择待审批的密钥')
    return
  }

  try {
    const { value: reason } = await ElMessageBox.prompt(
      `确定要批量拒绝 ${pendingKeys.length} 个密钥申请吗？请输入拒绝原因：`,
      '确认批量拒绝',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        inputType: 'textarea',
        inputValidator: (value) => {
          if (!value || !value.trim()) {
            return '请输入拒绝原因'
          }
          return true
        }
      }
    )

    const response = await batchApproveApiKey(pendingKeys, false, reason)
    if (response.code === 200) {
      ElMessage.success(`已批量拒绝 ${pendingKeys.length} 个申请`)
      selectedKeys.value = []
      await loadApiKeys() // 重新加载列表
    } else {
      ElMessage.error(response.message || '批量拒绝失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('批量拒绝失败:', error)
      ElMessage.error('批量拒绝失败')
    }
  }
}

const disableKey = async (apiKey: ApiKeyInfo) => {
  try {
    await ElMessageBox.confirm(
      `确定要禁用密钥"${apiKey.keyName}"吗？禁用后将无法使用此密钥访问API。`,
      '确认禁用',
      {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }
    )

    const response = await disableApiKey(apiKey.id)
    if (response.code === 200) {
      ElMessage.success('密钥已禁用')
      await loadApiKeys() // 重新加载列表
    } else {
      ElMessage.error(response.msg || '禁用失败')
    }
  } catch (error) {
    if (error !== 'cancel') {
      console.error('禁用密钥失败:', error)
      ElMessage.error('禁用密钥失败')
    }
  }
}

const viewKeyDetails = async (apiKey: ApiKeyInfo) => {
  try {
    // 获取完整的密钥信息（包含keyValue）
    const response = await getApiKeyInfo(apiKey.id)
    if (response.code === 200) {
      detailDialog.value.apiKey = response.data
      detailDialog.value.visible = true
      detailDialog.value.showKey = false
    } else {
      ElMessage.error(response.msg || '获取密钥详情失败')
    }
  } catch (error) {
    console.error('获取密钥详情失败:', error)
    ElMessage.error('获取密钥详情失败')
  }
}

const closeDetailDialog = () => {
  detailDialog.value.visible = false
  detailDialog.value.apiKey = null
  detailDialog.value.showKey = false
}

const copyApiKey = async () => {
  if (!detailDialog.value.apiKey?.keyValue) return

  try {
    await navigator.clipboard.writeText(detailDialog.value.apiKey.keyValue)
    ElMessage.success('API密钥已复制到剪贴板')
  } catch (error) {
    console.error('复制失败:', error)
    ElMessage.error('复制失败，请手动复制')
  }
}

// 监听筛选条件变化，重置分页和选择
watch(filters, () => {
  pagination.value.current = 1
  selectedKeys.value = []
  selectAll.value = false
}, { deep: true })

// 监听分页变化，重置选择
watch(() => pagination.value.current, () => {
  selectedKeys.value = []
  selectAll.value = false
})

// 初始化
onMounted(() => {
  loadApiKeys()
})
</script>

<style scoped>
.unified-api-key-management {
  padding: 0;
}

.config-header {
  border-bottom: 1px solid #e5e7eb;
  padding: 16px;
}

.header-content {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
}

.header-left {
  flex: 1;
}

.header-title {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 24px;
  font-weight: 700;
  color: #1f2937;
  margin: 0 0 8px 0;
}

.header-title i {
  color: #3b82f6;
  font-size: 28px;
}

.header-subtitle {
  color: #6b7280;
  font-size: 16px;
  line-height: 1.6;
  margin: 0;
}

.section-header h2 {
  color: white;
  margin: 0;
}

.section-header p {
  color: rgba(255, 255, 255, 0.9);
  margin: 0;
}

.stats-grid {
  margin-bottom: 2rem;
}

.stat-card {
  transition: all 0.3s ease;
  cursor: pointer;
}

.stat-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.filter-section {
  transition: all 0.3s ease;
}

.filter-section:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.api-key-list {
  transition: all 0.3s ease;
}

.api-key-list:hover {
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.table-header {
  position: sticky;
  top: 0;
  z-index: 10;
  background: #f9fafb;
}

.table-row:hover {
  background-color: #f8fafc;
}

/* 响应式设计 */
@media (max-width: 1024px) {
  .stats-grid {
    grid-template-columns: repeat(3, 1fr);
    gap: 1rem;
  }

  .stat-card {
    padding: 1rem;
  }

  .filter-section {
    padding: 1rem;
  }

  .table-header,
  .table-row {
    grid-template-columns: repeat(6, 1fr);
    gap: 2px;
    font-size: 0.75rem;
  }

  .col-span-2 {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .stats-grid {
    grid-template-columns: repeat(2, 1fr);
  }

  .section-header h2 {
    font-size: 1.5rem;
  }

  .section-header p {
    font-size: 0.875rem;
  }

  .table-header,
  .table-row {
    grid-template-columns: 1fr;
    gap: 0.5rem;
  }

  .table-header > div,
  .table-row > div {
    grid-column: span 1;
    padding: 0.5rem;
  }
}

@media (max-width: 480px) {
  .stats-grid {
    grid-template-columns: 1fr;
  }
}

/* 为每个统计卡片添加延迟动画 */
.stat-card:nth-child(1) { animation-delay: 0.1s; }
.stat-card:nth-child(2) { animation-delay: 0.2s; }
.stat-card:nth-child(3) { animation-delay: 0.3s; }
.stat-card:nth-child(4) { animation-delay: 0.4s; }
.stat-card:nth-child(5) { animation-delay: 0.5s; }

/* 表格行动画 */
.table-row {
  animation: slideInLeft 0.4s ease-out;
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}
</style>
