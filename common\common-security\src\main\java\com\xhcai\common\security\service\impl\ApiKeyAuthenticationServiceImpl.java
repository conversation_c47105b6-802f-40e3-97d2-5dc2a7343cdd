package com.xhcai.common.security.service.impl;

import java.security.MessageDigest;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Base64;
import java.util.Date;
import java.util.HashMap;
import java.util.Map;

import com.xhcai.common.core.exception.BusinessException;
import com.xhcai.common.security.utils.JwtUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.security.core.userdetails.UserDetails;
import org.springframework.stereotype.Service;

import com.xhcai.common.security.annotation.RequiresApiKey;
import com.xhcai.common.security.service.ApiKeyAuthenticationService;

import io.jsonwebtoken.Claims;
import io.jsonwebtoken.Jwts;
import io.jsonwebtoken.SignatureAlgorithm;
import io.jsonwebtoken.security.Keys;
import javax.crypto.SecretKey;

/**
 * API密钥认证服务实现
 *
 * <AUTHOR>
 * @since 1.0.0
 */
@Service
public class ApiKeyAuthenticationServiceImpl implements ApiKeyAuthenticationService {

    private static final Logger logger = LoggerFactory.getLogger(ApiKeyAuthenticationServiceImpl.class);

    private static final String API_KEY_SECRET = "xhcai-api-key-secret-2024-very-long-secret-key-for-api-security-purposes";
    private static final long TIME_TOLERANCE_MINUTES = 30; // 30分钟时效性

    @Autowired
    private JwtUtils jwtUtils;

    @Override
    public UserDetails authenticateApiKey(String compositeApiKey, String[] targetTypes,
                                        String[] permissions, RequiresApiKey.Logical logical,
                                        String requestUri) {
        try {
            // 1. 验证复合密钥的时效性并获取原始密钥
            String originalApiKey = validateCompositeApiKey(compositeApiKey);

            // 2. 解析原始API密钥
            Claims claims = parseApiKey(originalApiKey);

            // 3. 验证密钥是否过期
            if (claims.getExpiration().before(new Date())) {
                throw new BusinessException("API密钥已过期");
            }

            // 4. 验证目标类型
            if (targetTypes != null && targetTypes.length > 0) {
                String targetType = claims.get("targetType", String.class);
                boolean typeMatched = false;
                for (String allowedType : targetTypes) {
                    if (allowedType.equals(targetType)) {
                        typeMatched = true;
                        break;
                    }
                }
                if (!typeMatched) {
                    throw new BusinessException("API密钥目标类型不匹配");
                }
            }

            // 5. 验证权限（这里简化处理，实际应该根据用户权限进行验证）
            if (permissions != null && permissions.length > 0) {
                // TODO: 实现权限验证逻辑
                logger.debug("权限验证: {}", String.join(",", permissions));
            }

            // 6. 构建用户详情
            return buildUserDetailsFromClaims(claims);

        } catch (Exception e) {
            logger.error("API密钥认证失败", e);
            throw new BusinessException("API密钥认证失败: " + e.getMessage());
        }
    }

    @Override
    public String generateApiKey(String userId, String username, String tenantId, String deptId,
                               String targetType, String targetId, LocalDateTime expiresAt) {
        try {
            Map<String, Object> claims = new HashMap<>();
            claims.put("userId", userId);
            claims.put("username", username);
            claims.put("tenantId", tenantId);
            claims.put("deptId", deptId);
            claims.put("targetType", targetType);
            claims.put("targetId", targetId);

            ZoneId zone = ZoneId.systemDefault();

            return Jwts.builder()
                    .claims(claims)
                    .subject(username)
                    .issuedAt(new Date())
                    .expiration(Date.from(expiresAt.atZone(zone).toInstant()))
                    .signWith(getSigningKey(), Jwts.SIG.HS256)
                    .compact();

        } catch (Exception e) {
            logger.error("生成API密钥失败", e);
            throw new BusinessException("生成API密钥失败: " + e.getMessage());
        }
    }

    @Override
    public String generateKeyHash(String apiKey) {
        try {
            MessageDigest md = MessageDigest.getInstance("SHA-256");
            byte[] hash = md.digest(apiKey.getBytes());
            StringBuilder hexString = new StringBuilder();
            for (byte b : hash) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            return hexString.toString();
        } catch (Exception e) {
            logger.error("生成密钥哈希失败", e);
            throw new BusinessException("生成密钥哈希失败: " + e.getMessage());
        }
    }

    @Override
    public String validateCompositeApiKey(String compositeApiKey) {
        try {
            // Base64解码复合密钥
            String decoded = new String(Base64.getDecoder().decode(compositeApiKey));

            // 分离原始密钥和时间戳
            int lastDotIndex = decoded.lastIndexOf('.');
            if (lastDotIndex <= 0) {
                throw new BusinessException("复合密钥格式错误");
            }

            String originalApiKey = decoded.substring(0, lastDotIndex);
            String timestampStr = decoded.substring(lastDotIndex + 1);

            // 验证时间戳
            long timestamp = Long.parseLong(timestampStr);
            long currentTime = System.currentTimeMillis();
            long timeDiff = Math.abs(currentTime - timestamp);

            if (timeDiff > TIME_TOLERANCE_MINUTES * 60 * 1000) {
                throw new BusinessException("密钥时效性验证失败，超过30分钟时限");
            }

            return originalApiKey;

        } catch (NumberFormatException e) {
            throw new BusinessException("复合密钥时间戳格式错误");
        } catch (Exception e) {
            logger.error("验证复合密钥失败", e);
            throw new BusinessException("验证复合密钥失败: " + e.getMessage());
        }
    }

    @Override
    public String createCompositeApiKey(String originalApiKey, long timestamp) {
        try {
            String composite = originalApiKey + "." + timestamp;
            return Base64.getEncoder().encodeToString(composite.getBytes());
        } catch (Exception e) {
            logger.error("创建复合密钥失败", e);
            throw new BusinessException("创建复合密钥失败: " + e.getMessage());
        }
    }

    /**
     * 解析API密钥
     */
    private Claims parseApiKey(String apiKey) {
        try {
            return Jwts.parser()
                    .verifyWith(getSigningKey())
                    .build()
                    .parseSignedClaims(apiKey)
                    .getPayload();
        } catch (Exception e) {
            logger.error("解析API密钥失败", e);
            throw new BusinessException("API密钥格式错误或已损坏");
        }
    }

    /**
     * 从Claims构建用户详情
     */
    private UserDetails buildUserDetailsFromClaims(Claims claims) {
        try {
            String userId = claims.get("userId", String.class);
            String username = claims.get("username", String.class);
            String tenantId = claims.get("tenantId", String.class);
            String deptId = claims.get("deptId", String.class);

            // 这里简化处理，实际应该根据用户ID查询完整的用户信息和权限
            return org.springframework.security.core.userdetails.User.builder()
                    .username(username)
                    .password("") // API密钥认证不需要密码
                    .authorities("ROLE_API_USER") // 给予基本的API用户角色
                    .build();

        } catch (Exception e) {
            logger.error("构建用户详情失败", e);
            throw new BusinessException("构建用户详情失败: " + e.getMessage());
        }
    }

    /**
     * 获取签名密钥
     */
    private SecretKey getSigningKey() {
        byte[] keyBytes = API_KEY_SECRET.getBytes();
        return Keys.hmacShaKeyFor(keyBytes);
    }
}