package com.xhcai.modules.system.enums;

import lombok.Getter;


/**
 * API密钥状态字典数据
 */
@Getter
public enum ApiKeyStatus {

    PENDING("pending", "待审核", 1),
    APPROVED("approved", "已通过", 2),
    REJECTED("rejected", "已拒绝", 3),
    DISABLED("disabled", "已禁用", 4);

    private final String code;
    private final String info;
    private final int sort;

    ApiKeyStatus(String code, String info, int sort) {
        this.code = code;
        this.info = info;
        this.sort = sort;
    }

    /**
     * 根据代码获取枚举
     */
    public static ApiKeyStatus getByCode(String code) {
        for (ApiKeyStatus value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }
}
