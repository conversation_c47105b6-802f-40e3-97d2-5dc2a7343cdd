package com.xhcai.modules.system.vo;

import com.xhcai.modules.system.entity.SysApiKey;
import com.xhcai.modules.system.enums.ApiKeyFrequency;
import com.xhcai.modules.system.enums.ApiKeyStatus;
import com.xhcai.modules.system.enums.ApiKeyTargetType;
import io.swagger.v3.oas.annotations.media.Schema;
import lombok.Data;

@Data
public class SysApiKeyVO extends SysApiKey {

    @Schema(description = "审批状态名称", example = "GPT-4智能助手")
    private String statusName;

    private String targetTypeName;

    private String accessFrequencyName;

    public String getStatusName() {
        ApiKeyStatus apiKeyStatus = ApiKeyStatus.getByCode(this.getStatus());
        if (apiKeyStatus != null) {
            return apiKeyStatus.getCode();
        }
        return this.getStatus();
    }

    public String getTargetTypeName() {
        ApiKeyTargetType apiKeyTargetType = ApiKeyTargetType.getByCode(this.getStatus());
        if (apiKeyTargetType != null) {
            return apiKeyTargetType.getCode();
        }
        return this.getTargetType();
    }

    public String getAccessFrequencyName() {
        ApiKeyFrequency apiKeyFrequency = ApiKeyFrequency.getByCode(this.getStatus());
        if (apiKeyFrequency != null) {
            return apiKeyFrequency.getCode();
        }
        return this.getAccessFrequency();
    }

}
