<template>
  <div class="knowledge-detail">
    <!-- 头部导航 -->
    <div class="detail-header">
      <div class="header-left">
        <button
          @click="goBack"
          class="p-2 text-gray-500 hover:text-gray-700 hover:bg-gray-100 rounded-lg transition-colors"
        >
          <svg class="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"></path>
          </svg>
        </button>
        <button class="category-toggle-btn" @click="toggleCategoryPanel" :class="{ active: categoryPanelVisible }" title="文件分类">
          <i class="fas fa-folder-tree"></i>
        </button>
        <div class="kb-info" v-if="knowledgeBase">
          <div class="kb-icon" :style="{ backgroundColor: knowledgeBase.iconBg }">
            {{ knowledgeBase.icon }}
          </div>
          <div class="kb-details">
            <h1 class="kb-title">{{ knowledgeBase.name }} <span class="kb-unit">{{ knowledgeBase.unit }}</span></h1>
            <p class="kb-description">{{ knowledgeBase.description || '暂无描述' }}</p>
          </div>
        </div>
      </div>
      <div class="header-actions">
        <!-- 统计信息 -->
        <div class="stats-info">
          <span class="stat-item">📄 {{ totalFileCount }} 个文件</span>
          <span class="stat-item">📊 {{ formatCharCount(totalCharCount) }} 字符</span>
          <span class="stat-item">🔗 {{ totalSegmentCount }} 个分段</span>
        </div>

        <!-- 搜索和过滤 -->
        <div class="search-filter-group">
          <div class="search-box">
            <i class="fas fa-search search-icon"></i>
            <input
              type="text"
              placeholder="搜索文件..."
              v-model="searchQuery"
              @input="handleSearch"
              class="search-input"
            >
          </div>
          <select v-model="fileTypeFilter" @change="handleFilter" class="filter-select">
            <option value="">全部类型</option>
            <option value="pdf">PDF</option>
            <option value="doc">Word</option>
            <option value="txt">文本</option>
            <option value="md">Markdown</option>
            <option value="xlsx">Excel</option>
          </select>
          <select v-model="segmentStatusFilter" @change="handleFilter" class="filter-select">
            <option value="">全部状态</option>
            <option value="processing">处理中</option>
            <option value="completed">已完成</option>
            <option value="failed">失败</option>
            <option value="pending">待处理</option>
          </select>
        </div>

        <!-- 操作按钮 -->
        <div class="action-buttons">
          <!-- 默认显示的按钮 -->
          <button class="btn btn-outline" @click="refreshData">
            <i class="fas fa-sync-alt"></i>
            刷新
          </button>
          <button class="btn btn-outline" @click="openRecallTest">
            <i class="fas fa-flask"></i>
            测试
          </button>
          <div class="more-actions-dropdown" ref="moreDatasourceRef">
            <button class="btn btn-primary more-btn" @click="openDataSourceFlow">
              <i class="fas fa-upload"></i>
              数据源
            </button>
            <div class="dropdown-menu" v-show="showDatasource" @click.stop>
              <button class="dropdown-item" @click="openFileUploadPage">
                <i class="fas fa-tasks"></i>
                本地文件
              </button>
              <button class="dropdown-item" @click="openWorkflow">
                <i class="fas fa-file-circle-minus"></i>
                FTP文件
              </button>
              <button class="dropdown-item" @click="openWorkflow">
                <i class="fas fa-file-archive"></i>
                Minio文件
              </button>
              <el-divider style="margin: 2px auto"/>
              <button class="dropdown-item" @click="openWorkflow">
                <i class="fas fa-database"></i>
                Mysql
              </button>
              <button class="dropdown-item" @click="openWorkflow">
                <i class="fas fa-database"></i>
                PostgresSQL
              </button>
              <button class="dropdown-item" @click="openWorkflow">
                <i class="fas fa-database"></i>
                Elasticsearch
              </button>
              <el-divider style="margin: 2px auto"/>
              <button v-for="workflow in customWorkflows" :key="workflow.id" class="dropdown-item" @click="openWorkflow(workflow)">
                <i class="fas fa-address-book"></i>
                {{workflow.name}}
              </button>
              <el-divider style="margin: 2px auto"/>
              <button class="dropdown-item" @click="createNewWorkflow">
                <i class="fas fa-address-book"></i>
                创建工作流
              </button>
            </div>
          </div>
          <!-- 更多操作下拉菜单 -->
          <div class="more-actions-dropdown" ref="moreActionsRef">
            <button class="btn btn-outline more-btn" @click="toggleMoreActions">
              <i class="fas fa-ellipsis-h"></i>
              更多
            </button>
            <div class="dropdown-menu" v-show="showMoreActions" @click.stop>
              <button class="dropdown-item" @click="openTaskManagement">
                <i class="fas fa-tasks"></i>
                任务
              </button>
              <button class="dropdown-item" @click="openConfigModal">
                <i class="fas fa-cog"></i>
                配置
              </button>
              <el-divider style="margin: 2px auto"/>
              <button class="dropdown-item" @click="batchSetPermissions" :disabled="selectedFiles.length === 0">
                <i class="fas fa-shield-alt"></i>
                权限
              </button>
              <button class="dropdown-item" @click="batchDownload" :disabled="selectedFiles.length === 0">
                <i class="fas fa-download"></i>
                下载
              </button>
              <button class="dropdown-item" @click="batchSegment" :disabled="selectedFiles.length === 0">
                <i class="fas fa-cut"></i>
                分段
              </button>
              <button class="dropdown-item" @click="batchVectorize" :disabled="selectedFiles.length === 0">
                <i class="fas fa-vector-square"></i>
                向量化
              </button>
              <el-divider style="margin: 2px auto"/>
              <button class="dropdown-item" @click="openCategoryMigration" :disabled="selectedFiles.length === 0">
                <i class="fas fa-folder-open"></i>
                迁移分类
              </button>
              <button class="dropdown-item danger" @click="batchDelete" :disabled="selectedFiles.length === 0">
                <i class="fas fa-trash"></i>
                删除
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>



    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 文件分类面板 -->
      <FileCategoryPanel
        :visible="categoryPanelVisible"
        :datasetId="datasetId"
        :total-file-count="totalFileCount"
        @close="closeCategoryPanel"
        @category-selected="handleCategorySelected"
      />

      <!-- 文件列表组件 -->
      <div class="file-list-container" :class="{ 'with-category-panel': categoryPanelVisible }">
        <FileListManager
          ref="fileListManagerRef"
          :key="loadDocumentsKey"
          :datasetId="datasetId"
          :search-query="searchQuery"
          :file-type-filter="fileTypeFilter"
          :segment-status-filter="segmentStatusFilter"
          :category-filter="selectedCategoryId"
          @file-selected="handleFileSelected"
          @files-selected="handleFilesSelected"
          @refresh-stats="refreshStats"
          @stats-updated="handleStatsUpdate"
          @permission-panel-open="handlePermissionPanelOpen"
          @file-content-view="handleFileContentView"
        />
      </div>
    </div>

    <!-- 文件分段管理侧边栏 -->
    <FileSegmentPanel
      v-if="selectedFile"
      :file="selectedFile"
      :visible="segmentPanelVisible"
      @close="closeSegmentPanel"
    />

    <!-- 文件权限管理面板 -->
    <FilePermissionPanel
      v-if="permissionPanelVisible"
      :file="selectedPermissionFile"
      :files="batchPermissionFiles"
      :is-batch="isBatchPermission"
      :visible="permissionPanelVisible"
      @close="closePermissionPanel"
      @permission-updated="handlePermissionUpdated"
    />

    <!-- 任务管理全屏弹窗 -->
    <TaskManagement
      v-if="taskManagementVisible"
      :visible="taskManagementVisible"
      :datasetId="datasetId"
      :knowledge-base-name="knowledgeBase?.name"
      @close="closeTaskManagement"
    />

    <!-- 知识库配置弹窗 -->
    <KnowledgeConfig
      v-if="configModalVisible"
      :visible="configModalVisible"
      :datasetId="datasetId"
      :knowledge-base="knowledgeBase"
      @close="closeConfigModal"
    />

    <!-- 分类迁移弹窗 -->
    <CategoryMigrationModal
      v-if="categoryMigrationVisible"
      :visible="categoryMigrationVisible"
      :selected-files="selectedFiles"
      :dataset-id="datasetId"
      @close="closeCategoryMigration"
      @migration-success="handleMigrationSuccess"
    />
  </div>
</template>

<script setup lang="ts">
import { ref, shallowRef,onMounted, computed } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {v4 as uuidv4} from 'uuid'
import { RagAPI } from '@/api/rag'
import KnowledgeAPI from '@/api/knowledge'
import FileListManager from '@/views/rag/components/FileListManager.vue'
import FileSegmentPanel from '@/views/rag/datasource/file/FileSegmentPanel.vue'
import FilePermissionPanel from '@/views/rag/components/FilePermissionPanel.vue'
import FileCategoryPanel from '@/views/rag/components/FileCategoryPanel.vue'
import TaskManagement from '@/views/rag/components/TaskManagement.vue'
import KnowledgeConfig from '@/views/rag/components/KnowledgeConfig.vue'
import CategoryMigrationModal from '@/views/rag/components/CategoryMigrationModal.vue'

import { formatCharCount, formatDate } from '@/utils/fileUtils'

// 路由相关
const route = useRoute()
const router = useRouter()

// 响应式数据
const knowledgeBase = ref<any>(null)
const selectedFile = ref<any>(null)
const fileListManagerRef = ref<any>(null)
const segmentPanelVisible = ref(false)
const loading = ref(false)
const searchQuery = ref('')
const fileTypeFilter = ref('')
const segmentStatusFilter = ref('')
const selectedFiles = ref<any[]>([])
const totalFileCount = ref(0)
const totalCharCount = ref(0)
const totalSegmentCount = ref(0)
const permissionPanelVisible = ref(false)
const selectedPermissionFile = ref<any>(null)
const selectedContentFile = ref<any>(null)
const categoryPanelVisible = ref(false)
const selectedCategoryId = ref<string | null>(null)
const batchPermissionFiles = ref<any[]>([])
const isBatchPermission = ref(false)
const showMoreActions = ref(false)
const showDatasource = ref(false)
const moreActionsRef = ref<HTMLElement | null>(null)
const moreDatasourceRef = ref<HTMLElement | null>(null)
const taskManagementVisible = ref(false)
const configModalVisible = ref(false)
const datasetName = ref('')
const loadDocumentsKey = ref('')
const categoryMigrationVisible = ref(false)

// 响应式数据
const customWorkflows = ref([
  {
    id: 'ftp-datasource',
    name: '矛盾纠纷数据',
    description: '从FTP服务器获取文件并处理',
    icon: 'fas fa-server',
    iconBg: 'linear-gradient(135deg, #f093fb 0%, #f5576c 100%)',
    nodeCount: 4,
    lastModified: new Date('2024-01-10'),
    status: 'active'
  }
])

// 计算属性
const datasetId = computed(() => route.params.id as string)

// 方法
const goBack = () => {
  router.push('/knowledge')
}

const refreshData = async () => {
  loadDocumentsKey.value = uuidv4()
  await loadKnowledgeBase()
  await refreshStats()
}

const loadKnowledgeBase = async () => {
  loading.value = true
  try {
    const response = await RagAPI.getDatasetById(datasetId.value)
    if (response.success) {
      // 映射数据格式
      const dataset = response.data
      knowledgeBase.value = {
        id: dataset.id,
        name: dataset.name,
        description: dataset.description || '',
        icon: getIconForDataset(dataset),
        iconBg: getIconBgForDataset(dataset),
        unit: mapDataSourceTypeToUnit(dataset.dataSourceType),
        owner: dataset.createdBy || '未知',
        createTime: formatDate(dataset.createdAt),
        updateTime: formatDate(dataset.updatedAt || dataset.createdAt),
        docCount: (dataset as any).documentCount || 0,
        charCount: Math.floor(((dataset as any).characterCount || 0) / 1000),
        appCount: 0,
        tags: dataset.tags || []
      }
    }
  } catch (error) {
    console.error('加载知识库详情失败:', error)
  } finally {
    loading.value = false
  }
}

const refreshStats = async () => {
  // 刷新统计信息
  await loadKnowledgeBase()
  // 这里可以添加更多统计信息的加载逻辑
}

const openDataSourceFlow = () => {
  // 路由到数据源流程页面
  // router.push({ path: `/knowledge/${datasetId.value}/datasource`, query: { name: datasetName.value} });
  showDatasource.value = !showDatasource.value
}

const handleFileSelected = (file: any) => {
  selectedFile.value = file
  segmentPanelVisible.value = true
}

const closeSegmentPanel = () => {
  segmentPanelVisible.value = false
  selectedFile.value = null
}

// 辅助方法（与Knowledge.vue中的方法保持一致）
const simpleHash = (str: string) => {
  let hash = 0
  for (let i = 0; i < str.length; i++) {
    const char = str.charCodeAt(i)
    hash = ((hash << 5) - hash) + char
    hash = hash & hash
  }
  return Math.abs(hash)
}

const getIconForDataset = (dataset: any) => {
  const icons = ['🤖', '📋', '📈', '🎓', '💰', '🏗️', '🎧', '⚖️', '📊', '📅', '🔒', '🤝', '🎨', '⚙️', '✅', '🏛️', '🔌', '🔍', '👥', '🛡️', '📱', '💼', '🗄️', '📝', '☁️']
  const hash = simpleHash(dataset.id.toString())
  return icons[hash % icons.length] || '📚'
}

const getIconBgForDataset = (dataset: any) => {
  const colors = ['#e3f2fd', '#f3e5f5', '#e8f5e8', '#fff3e0', '#fce4ec', '#e1f5fe', '#f1f8e9', '#fafafa', '#e8eaf6', '#fff8e1', '#ffebee', '#e0f2f1']
  const hash = simpleHash(dataset.id.toString())
  return colors[hash % colors.length] || '#f5f5f5'
}

const mapDataSourceTypeToUnit = (dataSourceType: string) => {
  const mapping: Record<string, string> = {
    'upload_file': '技术部',
    'web_crawl': '市场部',
    'api_import': '数据部',
    'database': '技术部'
  }
  return mapping[dataSourceType] || '未知部门'
}

const handleSearch = () => {
  // 将搜索参数传递给FileListManager组件
}

const handleFilter = () => {
  // 将过滤参数传递给FileListManager组件
}

const handleStatsUpdate = (stats: any) => {
  totalFileCount.value = stats.totalCount || 0
  totalCharCount.value = stats.totalCharCount || 0
  totalSegmentCount.value = stats.totalSegmentCount || 0
}

const handlePermissionPanelOpen = (file: any) => {
  // 设置单个文件权限模式
  isBatchPermission.value = false
  batchPermissionFiles.value = []
  selectedPermissionFile.value = file
  permissionPanelVisible.value = true
}

const closePermissionPanel = () => {
  permissionPanelVisible.value = false
  selectedPermissionFile.value = null
  batchPermissionFiles.value = []
  isBatchPermission.value = false
}

const handleFileContentView = (file: any) => {
  selectedContentFile.value = file
}

// 批量操作方法
const batchSetPermissions = () => {
  if (selectedFiles.value.length === 0) return

  // 设置批量权限模式
  isBatchPermission.value = true
  batchPermissionFiles.value = [...selectedFiles.value]
  selectedPermissionFile.value = null
  permissionPanelVisible.value = true
  showMoreActions.value = false
}

const batchDownload = async () => {
  if (selectedFiles.value.length === 0) return

  try {
    const documentIds = selectedFiles.value.map(f => f.id)
    const response = await KnowledgeAPI.batchDownloadDocuments(documentIds)

    if (response.success) {
      // 这里应该处理文件下载，比如打开下载链接
      window.open(response.data, '_blank')
    } else {
      alert('批量下载失败：' + response.message)
    }
  } catch (error) {
    console.error('批量下载失败:', error)
    alert('批量下载失败，请重试')
  }

  showMoreActions.value = false
}

const batchDelete = async () => {
  if (selectedFiles.value.length === 0) return

  const fileNames = selectedFiles.value.map(f => f.name).join(', ')
  if (confirm(`确定要删除这 ${selectedFiles.value.length} 个文件吗？\n${fileNames}`)) {
    try {
      const documentIds = selectedFiles.value.map(f => f.id)
      const response = await KnowledgeAPI.batchDeleteDocuments(documentIds)

      if (response.success) {
        // 重新加载文件列表
        fileListManagerRef.value?.refreshFiles()
        selectedFiles.value = []
        alert('批量删除成功')
      } else {
        alert('批量删除失败：' + response.message)
      }
    } catch (error) {
      console.error('批量删除失败:', error)
      alert('批量删除失败，请重试')
    }
  }

  showMoreActions.value = false
}

// 分类面板相关方法
const toggleCategoryPanel = () => {
  categoryPanelVisible.value = !categoryPanelVisible.value
}

const closeCategoryPanel = () => {
  categoryPanelVisible.value = false
}

const handleCategorySelected = (categoryId: string | null) => {
  selectedCategoryId.value = categoryId
}

// 权限面板相关方法
const handlePermissionUpdated = () => {
  // 权限更新后刷新文件列表
  refreshStats()
}

const handleFilesSelected = (files: any[]) => {
  selectedFiles.value = files
}

// 更多操作下拉菜单相关方法
const toggleMoreActions = () => {
  showMoreActions.value = !showMoreActions.value
}

const openConfigModal = () => {
  configModalVisible.value = true
}

const closeConfigModal = () => {
  configModalVisible.value = false
}

const openTaskManagement = () => {
  taskManagementVisible.value = true
  showMoreActions.value = false
}

const closeTaskManagement = () => {
  taskManagementVisible.value = false
}

const batchSegment = () => {
  if (selectedFiles.value.length === 0) return

  const fileNames = selectedFiles.value.map(f => f.name).join(', ')
  alert(`批量分段文件: ${fileNames}`)
  // 这里可以实现批量分段逻辑
  showMoreActions.value = false
}

const batchVectorize = () => {
  if (selectedFiles.value.length === 0) return

  const fileNames = selectedFiles.value.map(f => f.name).join(', ')
  alert(`批量向量化文件: ${fileNames}`)
  // 这里可以实现批量向量化逻辑
  showMoreActions.value = false
}

// 召回测试相关方法
const openRecallTest = () => {
  const route = router.resolve({name: "DatasetRecallTest"})
  window.open(route.href, 'recallTest-' + datasetName.value);
}

// 打开本地文件上传
const openFileUploadPage = () => {
  // 使用路由跳转到文档处理流程页面
  router.push({
    name: 'LocalFileFlow',
    query: {
      datasetId: datasetId.value,
      name: datasetName.value,
      batchId: uuidv4()
    }
  })
}

const createNewWorkflow = () => {
  const route = router.resolve({name: "CreateDatasetWorkFlow"})
  window.open(route.href, 'createworkflow' + datasetName.value);
}

// 打开工作流
const openWorkflow = (workflow: any) => {
  // 打开工作流运行状态页面
  router.push({name: "DatasetFlowStats", query: {
      datasetId: datasetId.value,
      datasetName:  datasetName.value,
    }})
}

// 分类迁移相关方法
const openCategoryMigration = () => {
  if (selectedFiles.value.length === 0) return

  categoryMigrationVisible.value = true
  showMoreActions.value = false
}

const closeCategoryMigration = () => {
  categoryMigrationVisible.value = false
}

const handleMigrationSuccess = () => {
  // 迁移成功后刷新文件列表和统计信息
  refreshData()
  selectedFiles.value = []
  categoryMigrationVisible.value = false
}

// 生命周期
onMounted(async () => {
  datasetName.value = route.query.name as string
  await loadKnowledgeBase()

  // 添加点击外部关闭下拉菜单的事件监听
  document.addEventListener('click', (event) => {
    if (moreActionsRef.value && !moreActionsRef.value.contains(event.target as Node)) {
      showMoreActions.value = false
    }

    if (moreDatasourceRef.value && !moreDatasourceRef.value.contains(event.target as Node)) {
      showDatasource.value = false
    }
  })
})
</script>

<style scoped>
.knowledge-detail {
  height: 100vh;
  display: flex;
  flex-direction: column;
  background: #f8fafc;
  position: relative;
}

.detail-header {
  background: white;
  height: 50px;
  padding: 0 24px;
  border-bottom: 1px solid #e2e8f0;
  display: flex;
  justify-content: space-between;
  align-items: center;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
  gap: 16px;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16px;
}

.category-toggle-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid #e2e8f0;
  background: white;
  border-radius: 6px;
  cursor: pointer;
  color: #64748b;
  font-size: 16px;
  transition: all 0.2s ease;
}

.category-toggle-btn:hover {
  background: #f1f5f9;
  color: #334155;
}

.category-toggle-btn.active {
  background: #eff6ff;
  border-color: #3b82f6;
  color: #3b82f6;
}

.kb-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.kb-icon {
  width: 24px;
  height: 24px;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 14px;
}

.kb-title {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
  display: flex;
  align-items: center;
  gap: 8px;
}

.kb-unit {
  font-size: 12px;
  font-weight: 500;
  color: #64748b;
  background: #f1f5f9;
  padding: 2px 8px;
  border-radius: 12px;
  border: 1px solid #e2e8f0;
}

.kb-description {
  display: none;
}

.header-actions {
  display: flex;
  align-items: center;
  justify-content: flex-end;
  gap: 16px;
  flex: 1;
  min-width: 0;
}

.stats-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.stat-item {
  font-size: 12px;
  color: #64748b;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: #f8fafc;
  border-radius: 4px;
  border: 1px solid #e2e8f0;
  white-space: nowrap;
}

.search-filter-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.search-box {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  color: #94a3b8;
  font-size: 14px;
}

.search-input {
  padding: 6px 8px 6px 28px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 12px;
  width: 150px;
  height: 28px;
}

.search-input:focus {
  outline: none;
  border-color: #3b82f6;
}

.filter-select {
  padding: 4px 8px;
  border: 1px solid #e2e8f0;
  border-radius: 4px;
  font-size: 12px;
  background: white;
  min-width: 100px;
  height: 28px;
}

.filter-select:focus {
  outline: none;
  border-color: #3b82f6;
}

.action-buttons {
  display: flex;
  gap: 8px;
}

.btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 12px;
  font-weight: 500;
  transition: all 0.2s ease;
  height: 28px;
}

.btn-outline {
  background: white;
  color: #3b82f6;
  border: 1px solid #3b82f6;
}

.btn-outline:hover {
  background: #3b82f6;
  color: white;
}

.btn-primary {
  background: #3b82f6;
  color: white;
}

.btn-primary:hover {
  background: #2563eb;
}

.btn.danger {
  color: #dc2626;
  border-color: #dc2626;
}

.btn.danger:hover {
  background: #dc2626;
  color: white;
}

/* 更多操作下拉菜单样式 */
.more-actions-dropdown {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  top: 100%;
  right: 0;
  background: white;
  border: 1px solid #e2e8f0;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 1000;
  min-width: 120px;
  padding: 4px 0;
  margin-top: 4px;
}

.dropdown-item {
  display: flex;
  align-items: center;
  gap: 8px;
  width: 100%;
  padding: 8px 12px;
  border: none;
  background: none;
  color: #374151;
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.dropdown-item:hover:not(:disabled) {
  background: #f1f5f9;
  color: #1e293b;
}

.dropdown-item:disabled {
  color: #9ca3af;
  cursor: not-allowed;
  opacity: 0.5;
}

.dropdown-item.danger {
  color: #dc2626;
}

.dropdown-item.danger:hover:not(:disabled) {
  background: #fef2f2;
  color: #dc2626;
}



.main-content {
  flex: 1;
  overflow: visible; /* 改为 visible，允许分页组件显示 */
  display: flex;
  flex-direction: column;
  min-height: 0; /* 确保flex子项可以收缩 */
  position: relative;
}

.file-list-container {
  flex: 1;
  transition: margin-left 0.3s ease;
}

.file-list-container.with-category-panel {
  margin-left: 320px; /* 为分类面板留出空间 */
}


.upload-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1e293b;
}

.upload-files h4 {
  margin: 0 0 16px 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.file-name {
  font-weight: 500;
  color: #1e293b;
}

/* 文件内容查看模态框样式 */
.file-content-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20px;
}

.file-content-modal-content {
  background: white;
  border-radius: 12px;
  width: 90%;
  height: 90%;
  max-width: 1200px;
  max-height: 800px;
  overflow: hidden;
  box-shadow: 0 20px 40px rgba(0, 0, 0, 0.15);
  display: flex;
  flex-direction: column;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16px 20px;
  border-bottom: 1px solid #e2e8f0;
  background: #f8fafc;
}

.modal-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.modal-header-file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  font-size: 24px;
  width: 40px;
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
}

.file-name {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: #1e293b;
}

.file-meta {
  font-size: 12px;
  color: #64748b;
  margin-top: 4px;
}

.file-meta span {
  margin: 0 4px;
}

.modal-body {
  flex: 1;
  overflow: hidden;
}
</style>
